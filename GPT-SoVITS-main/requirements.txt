numpy==1.23.4
scipy
tensorboard
librosa==0.9.2
numba==0.56.4
pytorch-lightning
gradio>=4.0,<=4.24.0
ffmpeg-python
onnxruntime; sys_platform == 'darwin'
onnxruntime-gpu; sys_platform != 'darwin'
tqdm
funasr==1.0.27
cn2an
pypinyin
pyopenjtalk>=0.3.4
g2p_en
torchaudio
modelscope==1.10.0
sentencepiece
transformers
chardet
PyYAML
psutil
jieba_fast
jieba
LangSegment>=0.2.0
Faster_Whisper
wordsegment
rotary_embedding_torch
ToJyutping 
g2pk2
ko_pron
opencc; sys_platform != 'linux'
opencc==1.1.1; sys_platform == 'linux'
python_mecab_ko; sys_platform != 'win32'
fastapi<0.112.2
