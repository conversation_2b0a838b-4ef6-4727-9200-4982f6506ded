{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net (onnx_dereverb): reverberación estéreo, la mejor opción; no puede eliminar reverberación mono", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho: Eliminar el efecto de retardo. Aggressive elimina más que Normal, DeReverb elimina reverberación adicional, puede eliminar reverberación mono, pero no limpia bien la reverberación de placa de alta frecuencia", "*GPT模型列表": "*Lista de modelos GPT", "*SoVITS模型列表": "*Lista de modelos SoVITS", "*实验/模型名": "*Nombre del experimento/modelo", "*文本标注文件": "*Archivo de etiquetado de texto", "*训练集音频文件目录": "*Directorio de archivos de audio de entrenamiento", "*请上传并填写参考信息": "*Por favor, suba y complete la información de referencia", "*请填写需要合成的目标文本和语种模式": "*Por favor, complete el texto objetivo a sintetizar y el modo de idioma", ".list标注文件的路径": "Ruta del archivo de anotación .list", ".限制范围越小判别效果越好。": "#!.限制范围越小判别效果越好。", "0-前置数据集获取工具": "0-Herramienta de obtención de conjunto de datos previo", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-Herramienta de separación de voz y acompañamiento UVR5 y eliminación de reverberación y retardo", "0b-语音切分工具": "0b-Herramienta de división de voz", "0bb-语音降噪工具": "0bb-Herramienta de reducción de ruido de voz", "0c-中文批量离线ASR工具": "0c-Herramienta de ASR en lote fuera de línea en chino", "0d-语音文本校对标注工具": "0d-Herramienta de corrección y etiquetado de texto de voz", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1A-训练集格式化工具": "1A-Herramienta de formateo del conjunto de datos de entrenamiento", "1Aa-文本内容": "1Aa-Contenido del texto", "1Aabc-训练集格式化一键三连": "1Aabc-Formateo del conjunto de datos de entrenamiento en un solo paso", "1Ab-SSL自监督特征提取": "1Ab-Extracción de características auto-supervisada SSL", "1Ac-语义token提取": "1Ac-Extracción de tokens semánticos", "1B-微调训练": "1B-Entrenamiento de ajuste fino", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-Entrenamiento de SoVITS. Los archivos de modelo para compartir se encuentran en SoVITS_weights.", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-Entrenamiento de GPT. Los archivos de modelo para compartir se encuentran en GPT_weights.", "1C-推理": "1C-Inferencia", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1. El modelo DeEcho-DeReverb tarda casi el doble que los otros dos modelos DeEcho", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1. <PERSON><PERSON><PERSON> voz principal: seleccione este para audio sin coros, retiene mejor la voz principal que HP5. Incluye dos modelos, HP2 y HP3; HP3 puede filtrar ligeramente el acompañamiento pero retiene mejor la voz principal que HP2", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-Cambio de voz", "2、MDX-Net-Dereverb模型挺慢的；": "2. El modelo MDX-Net-Dereverb es bastante lento", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2. Solo retener voz principal: seleccione este para audio con coros, puede debilitar la voz principal. Incluye un modelo HP5", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3. La configuración más limpia recomendada es primero MDX-Net, luego DeEcho-Aggressive", "3、去混响、去延迟模型（by FoxJoy）：": "3. Modelos de eliminación de reverberación y retardo (por FoxJoy)", "ASR 模型": "Modelo ASR", "ASR 模型尺寸": "Tamaño del modelo ASR", "ASR 语言设置": "Configuración del idioma ASR", "ASR进程输出信息": "Información de salida del proceso ASR", "GPT模型列表": "Lista de modelos GPT", "GPT训练进程输出信息": "Información de salida del proceso de entrenamiento de GPT", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "Parámetros de muestreo de GPT (no demasiado bajos cuando no hay texto de referencia. Use los valores por defecto si no está seguro):", "GPU卡号,只能填1个整数": "Número de tarjeta GPU, solo se puede ingresar un número entero", "GPU卡号以-分割，每个卡号一个进程": "Número de tarjeta GPU separado por '-', cada número de tarjeta es un proceso", "SSL进程输出信息": "Información de salida del proceso SSL", "SoVITS模型列表": "Lista de modelos SoVITS", "SoVITS训练进程输出信息": "Información de salida del proceso de entrenamiento de SoVITS", "TTS推理WebUI进程输出信息": "Información de salida del proceso de interfaz web de inferencia TTS", "TTS推理进程已关闭": "Proceso de inferencia TTS cerrado", "TTS推理进程已开启": "Proceso de inferencia TTS iniciado", "UVR5已关闭": "UVR5 está deshabilitado", "UVR5已开启": "UVR5 está habilitado", "UVR5进程输出信息": "Información de salida del proceso UVR5", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix: proporción de mezcla de audio normalizado que entra", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size: cómo calcular la curva de volumen, cuanto más pequeño, mayor precisión pero mayor carga computacional (mayor precisión no significa mejor rendimiento)", "max:归一化后最大值多少": "max: valor máximo después de la normalización", "max_sil_kept:切完后静音最多留多长": "max_sil_kept: duración máxima del silencio después del corte", "min_interval:最短切割间隔": "min_interval: intervalo mínimo de corte", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length: longitud mínima de cada segmento; si el primer segmento es demasiado corto, se une al siguiente hasta superar este valor", "temperature": "temperatura", "threshold:音量小于这个值视作静音的备选切割点": "umbral: puntos de corte alternativos considerados como silencio si el volumen es menor que este valor", "top_k": "top_k", "top_p": "top_p", "一键三连进程输出信息": "Información de salida del proceso de triple acción", "不切": "No cortar", "中文": "Chino", "中文教程文档：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e": "Documentación del tutorial en chino: https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e", "中英混合": "Chino e inglés mezclados", "也可批量输入音频文件, 二选一, 优先读文件夹": "También se pueden ingresar archivos de audio por lotes, seleccionar uno, prioridad para leer carpetas", "人声伴奏分离批量处理， 使用UVR5模型。": "Procesamiento por lotes de separación de voz y acompañamiento utilizando el modelo UVR5", "人声提取激进程度": "Nivel de agresividad en la extracción de voz", "以下文件或文件夹不存在": "No Existe Tal Archivo o Carpeta", "以下模型不存在:": "No Existe tal Modelo:", "伴奏人声分离&去混响&去回声": "Separación de acompañamiento y voz principal y eliminación de reverberación y eco", "使用无参考文本模式时建议使用微调的GPT，听不清参考音频说的啥(不晓得写啥)可以开。<br>开启后无视填写的参考文本。": "Se recomienda usar un GPT ajustado en modo sin texto de referencia; habilítelo si no puede entender el audio de referencia (si no sabe qué escribir). Una vez habilitado, ignorará el texto de referencia ingresado.", "保存频率save_every_epoch": "Frecuencia de guardado (cada epoch)", "关闭TTS推理WebUI": "Cerrar TTS Inference WebUI", "关闭UVR5-WebUI": "Cerrar UVR5-WebUI", "关闭打标WebUI": "Cerrar Labeling WebUI", "凑50字一切": "Todo para alcanzar las 50 palabras", "凑四句一切": "Completa cuatro oraciones para rellenar todo", "切分后的子音频的输出根目录": "Directorio raíz de salida de los sub-audios después de la división", "切割使用的进程数": "Número de procesos utilizados para la división", "刷新模型路径": "Actualizar la ruta del modelo", "前端处理后的文本(每句):": "Texto después del procesamiento previo (por frase):", "去混响/去延迟，附：": "Eliminación de reverberación/retardo, incluye:", "参考音频在3~10秒范围外，请更换！": "El audio de referencia está fuera del rango de 3 a 10 segundos, ¡por favor cámbielo!", "参考音频的文本": "Texto de referencia del audio", "参考音频的语种": "Idioma del audio de referencia", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "Opcional: Sube varios archivos de audio de referencia arrastrándolos y soltándolos (se recomienda que sean del mismo género) y promedia sus tonos. Si esta opción se deja en blanco, el tono será controlado por el único audio de referencia a la izquierda. Si se está afinando el modelo, se recomienda que todos los archivos de audio de referencia tengan tonos dentro del conjunto de entrenamiento de ajuste fino; se puede ignorar el modelo preentrenado.", "合成语音": "Sín<PERSON>is de voz", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "Ejemplo de formato de ruta de carpeta válida: E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例 (simplemente copie desde la barra de direcciones del administrador de archivos).", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "Ingrese el directorio donde se encuentran los audios después de la división. La ruta completa de los archivos de audio leídos = este directorio + nombre de archivo correspondiente en el archivo .list (no la ruta completa). Si se deja en blanco, se utilizará la ruta completa del archivo .list.", "多语种混合": "Mezcla de varios idiomas", "多语种混合(粤语)": "<PERSON><PERSON><PERSON><PERSON> (Cantonés)", "实际输入的参考文本:": "Texto de referencia realmente ingresado:", "实际输入的目标文本(切句后):": "Texto objetivo realmente ingresado (después de dividir en frases):", "实际输入的目标文本(每句):": "Texto objetivo realmente ingresado (por frase):", "实际输入的目标文本:": "Texto objetivo realmente ingresado:", "导出文件格式": "Formato de archivo de exportación", "开启GPT训练": "Iniciar entrenamiento de GPT", "开启SSL提取": "Habilitar la extracción SSL", "开启SoVITS训练": "Iniciar entrenamiento de SoVITS", "开启TTS推理WebUI": "Abrir TTS Inference WebUI", "开启UVR5-WebUI": "Abrir UVR5-WebUI", "开启一键三连": "Habilitar un solo paso de formateo", "开启打标WebUI": "Abrir Labeling WebUI", "开启文本获取": "Habilitar la obtención de texto", "开启无参考文本模式。不填参考文本亦相当于开启。": "Habilitar el modo sin texto de referencia. No llenar el texto de referencia también lo habilita.", "开启离线批量ASR": "Habilitar ASR en lote fuera de línea", "开启语义token提取": "Habilitar la extracción de tokens semánticos", "开启语音切割": "Habilitar la división de voz", "开启语音降噪": "Habilitar la reducción de ruido de voz", "怎么切": "Cómo cortar", "总训练轮数total_epoch": "Número total de épocas de entrenamiento", "总训练轮数total_epoch，不建议太高": "Número total de épocas de entrenamiento, no se recomienda demasiado alto", "打标工具WebUI已关闭": "Interfaz web de la herramienta de etiquetado cerrada", "打标工具WebUI已开启": "Interfaz web de la herramienta de etiquetado iniciada", "打标工具进程输出信息": "Información de salida del proceso de la herramienta de etiquetado", "指定输出主人声文件夹": "Especificar carpeta de salida de voz principal", "指定输出非主人声文件夹": "Especificar carpeta de salida de no voz principal", "按中文句号。切": "Cortar según puntos en chino", "按标点符号切": "Cortar según los signos de puntuación", "按英文句号.切": "Cortar por puntos en inglés.", "数据类型精度": "precisión del tipo de datos", "文本模块学习率权重": "Peso de la tasa de aprendizaje del módulo de texto", "文本进程输出信息": "Información de salida del proceso de obtención de texto", "施工中，请静候佳音": "En construcción, por favor espere pacientemente", "日文": "Japonés", "日英混合": "Mezcla de japonés e inglés", "是否仅保存最新的ckpt文件以节省硬盘空间": "¿Guardar solo el último archivo ckpt para ahorrar espacio en disco?", "是否在每次保存时间点将最终小模型保存至weights文件夹": "¿Guardar el modelo final pequeño en la carpeta de pesos en cada punto de guardado?", "是否开启dpo训练选项(实验性)": "¿Habilitar la opción de entrenamiento dpo (experimental)?", "是否直接对上次合成结果调整语速和音色。防止随机性。": "¿Ajustar directamente la velocidad del habla y el tono del último resultado de síntesis? Para prevenir la aleatoriedad.", "显卡信息": "Información de la tarjeta gráfica", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Este software es de código abierto bajo la licencia MIT. El autor no tiene control sobre el software. El usuario que lo utilice o distribuya, y el que genere sonidos a partir del software, asume toda la responsabilidad. <br>Si no acepta estos términos, no puede utilizar ni hacer referencia a ningún código o archivo dentro del paquete de software. Consulte el archivo <b>LICENSE</b> en el directorio raíz para obtener más detalles.", "模型": "<PERSON><PERSON>", "模型分为三类：": "Los modelos se dividen en tres categorías:", "模型切换": "Cambio de modelo", "每张显卡的batch_size": "Tamaño de lote por tarjeta gráfica", "版本": "Versión", "粤英混合": "Mezcla Cantonés-Inglés", "粤语": "Cantonés", "终止ASR进程": "Terminar el proceso ASR", "终止GPT训练": "Detener entrenamiento de GPT", "终止SSL提取进程": "Terminar el proceso de extracción SSL", "终止SoVITS训练": "Detener entrenamiento de SoVITS", "终止一键三连": "Terminar el proceso de un solo paso de formateo", "终止文本获取进程": "Terminar el proceso de obtención de texto", "终止语义token提取进程": "Terminar el proceso de extracción de tokens semánticos", "终止语音切割": "Terminar la división de voz", "终止语音降噪进程": "Terminar el proceso de reducción de ruido de voz", "缺少Hubert数据集": "Falta el Conjunto de Datos de Hubert", "缺少语义数据集": "Falta el Conjunto de Datos Semánticos", "缺少音素数据集": "Falta el Conjunto de Datos de Fonemas", "缺少音频数据集": "Falta el Conjunto de Datos de Audio", "英文": "Inglés", "语义token提取进程输出信息": "Información de salida del proceso de extracción de tokens semánticos", "语速": "Velocidad de <PERSON>bla", "语速调整，高为更快": "Ajustar la velocidad de habla, más alta para más rápido", "语音切割进程输出信息": "Información de salida del proceso de división de voz", "语音降噪进程输出信息": "Información de salida del proceso de reducción de ruido de voz", "请上传3~10秒内参考音频，超过会报错！": "Por favor, suba un audio de referencia de entre 3 y 10 segundos, ¡más de eso causará un error!", "请上传参考音频": "<PERSON><PERSON>, Suba el Audio de Referencia", "请填入推理文本": "<PERSON><PERSON>, Ingrese el Texto Objetivo", "请填入正确的List路径": "<PERSON><PERSON> F<PERSON>, Introduzca la Ruta Correcta de la Lista", "请填入正确的音频文件夹路径": "<PERSON><PERSON> Fav<PERSON>, Introduzca la Ruta Correcta de la Carpeta de Audio", "请输入有效文本": "Por favor, introduzca un texto válido", "路径不能为空": "Se Espera que la Ruta No Esté Vacía", "路径错误": "<PERSON><PERSON><PERSON>", "转换": "Convertir", "输入待处理音频文件夹路径": "Ingrese la ruta de la carpeta de audio a procesar", "输入文件夹路径": "Ingrese la ruta de la carpeta", "输出logs/实验名目录下应有23456开头的文件和文件夹": "Debe haber archivos y carpetas que comiencen con 23456 en el directorio logs/nombre del experimento", "输出信息": "Información de salida", "输出文件夹路径": "<PERSON>uta de la carpeta de salida", "输出的语音": "Audio de salida", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "Seleccione el modelo almacenado en SoVITS_weights y GPT_weights después del entrenamiento. Uno de ellos es el modelo base, útil para experimentar con TTS de 5 segundos sin entrenamiento.", "降噪结果输出文件夹": "Carpeta de salida de los resultados de reducción de ruido", "降噪音频文件输入文件夹": "Carpeta de entrada de archivos de audio para reducción de ruido", "需要合成的文本": "Texto a sintetizar", "需要合成的语种": "Idioma para la síntesis", "韩文": "<PERSON><PERSON>", "韩英混合": "<PERSON><PERSON><PERSON><PERSON>-Inglés", "音频加载失败": "Error al Cargar el Audio", "音频自动切分输入路径，可文件可文件夹": "Ruta de entrada para la división automática de audio, puede ser un archivo o una carpeta", "预训练的GPT模型路径": "Ruta del modelo GPT preentrenado", "预训练的SSL模型路径": "Ruta del modelo SSL preentrenado", "预训练的SoVITS-D模型路径": "Ruta del modelo SoVITS-D preentrenado", "预训练的SoVITS-G模型路径": "Ruta del modelo SoVITS-G preentrenado", "预训练的中文BERT模型路径": "Ruta del modelo BERT en chino preentrenado"}