{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net(onnx_dereverb):İki kanallı yankılar için en iyi seçimdir, ancak tek kanallı yankıları ortadan kaldıramaz;", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho:<PERSON><PERSON>ik<PERSON> etki<PERSON>ini giderir. Aggressive, Normal'dan daha kapsamlı bir <PERSON><PERSON><PERSON> gider<PERSON>, DeReverb ek olarak yankıyı giderir, tek kanallı yankıyı giderebilir, ancak yüksek frekanslı plaka yankısını tamamen gideremez.", "*GPT模型列表": "*GPT model listesi", "*SoVITS模型列表": "*SoVITS model listesi", "*实验/模型名": "*Deney/model adı", "*文本标注文件": "*<PERSON><PERSON> et<PERSON><PERSON>ı", "*训练集音频文件目录": "*Eğitim seti ses dosyası dizini", "*请上传并填写参考信息": "*Lütfen referans bilgilerini yükleyin ve doldurun", "*请填写需要合成的目标文本和语种模式": "*Lütfen sentezlenecek hedef metni ve dil modunu giriniz.", ".list标注文件的路径": ".list etiketleme dos<PERSON>ı<PERSON>ın yolu", ".限制范围越小判别效果越好。": "Daha az çok dilli olmak daha iyidir", "0-前置数据集获取工具": "0-<PERSON><PERSON> veri seti alma aracı", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-UVR5 vokal eşlik ayırma & yankıyı giderme gecikme aracı", "0b-语音切分工具": "0b-<PERSON><PERSON> b<PERSON>ı", "0bb-语音降噪工具": "0bb-Ses gürültü azaltma aracı", "0c-中文批量离线ASR工具": "0c-Çince toplu offline ASR aracı", "0d-语音文本校对标注工具": "0d-<PERSON><PERSON> ve metin düzeltme etiketleme aracı", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1A-训练集格式化工具": "1A-Eğitim seti formatlama aracı", "1Aa-文本内容": "1Aa-<PERSON><PERSON>", "1Aabc-训练集格式化一键三连": "1Aabc-Eğitim seti formatlama tek tuşla üçleme", "1Ab-SSL自监督特征提取": "1Ab-SSL kendi kendine denetimli özellik çıkarma", "1Ac-语义token提取": "1Ac-Anlamsal <PERSON>", "1B-微调训练": "1B-Fine-tuning eğitimi", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-SoVITS eğitimi. Paylaşım için model dosyaları SoVITS_weights altında çıkarılır.", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-GPT eğitimi. Paylaşım için model dosyaları GPT_weights altında çıkarılır.", "1C-推理": "1C-Çıkarım", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1. De<PERSON>cho-DeReverb modelinin i<PERSON>lem<PERSON>, di<PERSON><PERSON> iki DeEcho modelinin neredeyse iki katıdır;", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1. Se<PERSON> koruma: Arka vokal içermeyen sesler için bu seçeneği kullanın, ana sesi HP5'ten daha iyi korur. HP2 ve HP3 adlı iki model içerir; HP3, arka vokali biraz kaçırabilir ancak ana sesi HP2'ye göre biraz daha iyi korur;", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-Ses Değiştirme", "2、MDX-Net-Dereverb模型挺慢的；": "2. MDX-Net-Dereverb modeli oldukça ya<PERSON>ştır;", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2. <PERSON><PERSON><PERSON> ana sesi koruma: Arka vokalleri içeren sesler için bu seçeneği kullanın, ana sesi zayıflatabilir. İçinde HP5 modeli var;", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3. <PERSON><PERSON><PERSON><PERSON> en temiz konfigürasyon MDX-Net'in ardından DeEcho-Aggressive'dir.", "3、去混响、去延迟模型（by FoxJoy）：": "3. <PERSON><PERSON><PERSON> <PERSON> gec<PERSON> gider<PERSON> modeli (FoxJoy tarafından):", "ASR 模型": "ASR modeli", "ASR 模型尺寸": "ASR model boy<PERSON><PERSON>", "ASR 语言设置": "ASR dil ayarları", "ASR进程输出信息": "ASR işlemi çıktı bilgisi", "GPT模型列表": "GPT model listesi", "GPT训练进程输出信息": "GPT eğitimi işlemi çıktı bilgisi", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "GPT örnekleme parametreleri (referans metin olmadığında çok düşük olmamalıdır. <PERSON><PERSON> <PERSON>sayılanı kullanın):", "GPU卡号,只能填1个整数": "GPU kart numarası, sadece bir tamsayı girilebilir", "GPU卡号以-分割，每个卡号一个进程": "GPU kart numaralar<PERSON> - <PERSON><PERSON>, her kart numarası için bir işlem", "SSL进程输出信息": "SSL işlemi çıktı bilgisi", "SoVITS模型列表": "SoVITS model listesi", "SoVITS训练进程输出信息": "SoVITS eğitimi işlemi çıktı bilgisi", "TTS推理WebUI进程输出信息": "TTS çıkarımı WebUI işlemi çıktı bilgisi", "TTS推理进程已关闭": "TTS çıkarım işlemi kapatıldı", "TTS推理进程已开启": "TTS çıkarım işlemi başlatıldı", "UVR5已关闭": "UVR5 kapandı", "UVR5已开启": "UVR5 açıldı", "UVR5进程输出信息": "UVR5 işlem çıktı bilgisi", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix:Normalizasyondan sonraki sesin ne kadarlık bir oranı karıştırılsın", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size:Ses seviyesi eğrisi nasıl he<PERSON>, ne kadar küçükse hassasiyet o kadar yüksek ve hesaplama yükü o kadar artar (hassasiyet arttıkça etki mutlaka daha iyi olmaz)", "max:归一化后最大值多少": "max:<PERSON><PERSON><PERSON><PERSON><PERSON> sonra maksimum de<PERSON> ne kadar", "max_sil_kept:切完后静音最多留多长": "max_sil_kept:Kesimden sonra en fazla ne kadar sessizlik bırakılır", "min_interval:最短切割间隔": "min_interval:Minimum kesim aralığı", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length: b<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON>, ilk bölüm çok kısa ise, bu de<PERSON><PERSON> a<PERSON>ana kadar sonraki böl<PERSON><PERSON>le birleştirilir", "temperature": "temperature", "threshold:音量小于这个值视作静音的备选切割点": "threshold:<PERSON><PERSON> bu değerden düşükse sessiz olarak kabul edilen alternatif kesim noktası", "top_k": "top_k", "top_p": "top_p", "一键三连进程输出信息": "Tek tuşla üçleme işlemi çıktı bilgisi", "不切": "<PERSON><PERSON><PERSON>", "中文": "<PERSON><PERSON><PERSON>", "中文教程文档：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e": "<PERSON><PERSON><PERSON> öğ<PERSON> belge：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e", "中英混合": "Çince ve İngilizce karışık", "也可批量输入音频文件, 二选一, 优先读文件夹": "Ses dosyaları ayrıca toplu olarak, iki <PERSON>, öncelikli okuma klasörüyle içe aktarılabilir", "人声伴奏分离批量处理， 使用UVR5模型。": "Vokal ve akor ayırma toplu işleme, UVR5 modelini kullanarak.", "人声提取激进程度": "Vokal çıkarma agresiflik derecesi", "以下文件或文件夹不存在": "<PERSON><PERSON><PERSON> veya Klasör Yo<PERSON>", "以下模型不存在:": "Böyle bir model yok:", "伴奏人声分离&去混响&去回声": "Vokal/Müzik Ayrıştırma ve Yankı Giderme", "使用无参考文本模式时建议使用微调的GPT，听不清参考音频说的啥(不晓得写啥)可以开。<br>开启后无视填写的参考文本。": "Referans metin modu olmadan k<PERSON>ı<PERSON>, referans sesi net duyulmadığında (ne yazılacağı bilinmiyorsa) açık bırakılması önerilir, bu durumda girilen referans metni göz ardı edilir.", "保存频率save_every_epoch": "<PERSON><PERSON><PERSON> sıklığı save_every_epoch", "关闭TTS推理WebUI": "TTS Inference WebUI'yi <PERSON>", "关闭UVR5-WebUI": "UVR5-Web<PERSON><PERSON><PERSON><PERSON>", "关闭打标WebUI": "Labeling Web<PERSON>'<PERSON><PERSON>", "凑50字一切": "50 kelime bi<PERSON> ve kes", "凑四句一切": "<PERSON><PERSON><PERSON> c<PERSON>mleyi bir araya getirip kes", "切分后的子音频的输出根目录": "Bölündükten sonra alt ses dosyalarının çıktı kök dizini", "切割使用的进程数": "Kesim için kullanılan işlem sayısı", "刷新模型路径": "Model yolu yenile", "前端处理后的文本(每句):": "<PERSON><PERSON> <PERSON> tabi tutulan metin (her cüm<PERSON>):", "去混响/去延迟，附：": "Yankı giderme/Geçikme giderme, ek:", "参考音频在3~10秒范围外，请更换！": "Referans ses dosyası 3~10 saniye aralığının dışı<PERSON>, lütfen değiştirin!", "参考音频的文本": "Referans ses dos<PERSON><PERSON><PERSON><PERSON>n metni", "参考音频的语种": "Referans ses dos<PERSON>ı<PERSON>ın dili", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "İsteğe bağlı: Birden fazla referans ses dosyasını sürükleyip bırakarak yükleyin (aynı cinsiyetten olmaları önerilir) ve tonlarını ortalayın. Bu seçenek boş bırak<PERSON><PERSON><PERSON>, ton soldaki tek referans ses dosyası tarafından kontrol edilir. Modeli ince ayar yapıyorsanız, tüm referans ses dosyalarının ince ayar eğitim seti içindeki tonlara sahip olması önerilir; önceden eğitilmiş model dikkate alınmayabilir.", "合成语音": "<PERSON><PERSON> sentezi", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "Geçerli klasör yolu formatı örneği: E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例 (dosya yöneticisi adres çubuğundan kopyalayabilirsiniz).", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "Kesmeye uygun ses dosyalarının bulunduğu dizini doldurun! Okunan ses dosyasının tam yolu = bu dizin + list dosyasındaki dalga biçimiyle eşleşen dosya adı (tam yol değil). Boş bırakılırsa, .list dosyasındaki tam yol kullanılır.", "多语种混合": "Çok dilli karışım", "多语种混合(粤语)": "Çok dilli karışık (Yue)", "实际输入的参考文本:": "Gerçekten girilen referans metin:", "实际输入的目标文本(切句后):": "Gerçekten girilen hedef metin (cümleler kesildikten sonra):", "实际输入的目标文本(每句):": "Gerçekten girilen hedef metin (her c<PERSON><PERSON><PERSON>):", "实际输入的目标文本:": "Gerçekten girilen hedef metin:", "导出文件格式": "Dışa aktarma dosya formatı", "开启GPT训练": "GPT eğitimini başlat", "开启SSL提取": "SSL çıkarmayı başlat", "开启SoVITS训练": "SoVITS eğitimini başlat", "开启TTS推理WebUI": "TTS Inference WebUI'yi Aç", "开启UVR5-WebUI": "UVR5-WebUI'yi <PERSON>", "开启一键三连": "Tek tuşla üçlemeyi başlat", "开启打标WebUI": "Labeling WebUI'yi Aç", "开启文本获取": "<PERSON><PERSON> b<PERSON>", "开启无参考文本模式。不填参考文本亦相当于开启。": "Referans metni olmayan mod açık. Referans metni doldurulmazsa bu mod otomatik olarak açılır.", "开启离线批量ASR": "Offline toplu ASR başlat", "开启语义token提取": "Anlamsal token çıkarmayı başlat", "开启语音切割": "<PERSON><PERSON> kesi<PERSON> b<PERSON>", "开启语音降噪": "Ses gürültü azaltmayı başlat", "怎么切": "<PERSON><PERSON><PERSON><PERSON> kesi<PERSON>", "总训练轮数total_epoch": "Toplam eğitim turu sayısı total_epoch", "总训练轮数total_epoch，不建议太高": "Toplam eğitim turu sayısı total_epoch, çok yüksek önerilmez", "打标工具WebUI已关闭": "Etiketleme aracı WebUI'si kapatıldı", "打标工具WebUI已开启": "Etiketleme aracı WebUI'si açıldı", "打标工具进程输出信息": "Etiketleme aracı işlemi çıktı bilgisi", "指定输出主人声文件夹": "Vokal için çı<PERSON> klasörünü belirtin:", "指定输出非主人声文件夹": "Müzik ve diğer sesler için çıkış klasörünü belirtin:", "按中文句号。切": "<PERSON><PERSON><PERSON> dönem işaretine göre kes", "按标点符号切": "Noktalama işaretlerine göre kes", "按英文句号.切": "İngilizce nokta işaretine göre kes", "数据类型精度": "veri tü<PERSON><PERSON> doğruluğ<PERSON>", "文本模块学习率权重": "<PERSON>in modülü öğrenme oranı ağırlığı", "文本进程输出信息": "Metin işlemi çıktı bilgisi", "施工中，请静候佳音": "<PERSON><PERSON><PERSON><PERSON>, lütfen iyi haberler i<PERSON><PERSON> be<PERSON>", "日文": "Japonca", "日英混合": "Japonca ve İngilizce karışımı", "是否仅保存最新的ckpt文件以节省硬盘空间": "Sadece en yeni ckpt dosyasını kaydederek disk alanından tasarruf edilsin mi", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Her kayıt zamanında son k<PERSON><PERSON><PERSON><PERSON> modelin weights klasörüne kaydedilmesi gerekiyor mu", "是否开启dpo训练选项(实验性)": "dpo eğitim seçeneği açılsın mı? (deneysel)", "是否直接对上次合成结果调整语速和音色。防止随机性。": "Rastgeleliği önlemek için son sentez sonucunun konuşma hızını ve tonunu ayarlayın.", "显卡信息": "Ekran kartı bilgisi", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Bu yazılım MIT lisansı ile açık kaynaktır, yazar yazılım üzerinde herhangi bir kontrol gücüne sahip değildir, yazılımı kullanıcılar ve yazılım tarafından üretilen sesleri yayınlayanlar tüm sorumluluğu üstlenir. <br>Eğer bu şartları kabul etmiyorsanız, yazılım paketindeki hiçbir kodu veya dosyayı kullanamaz veya atıfta bulunamazsınız. Ayrıntılar için ana dizindeki <b>LICENSE</b>'ı görün.", "模型": "Model", "模型分为三类：": "Modeller üç türdedir:", "模型切换": "<PERSON> <PERSON><PERSON><PERSON>", "每张显卡的batch_size": "Her bir ekran kartı için batch_size", "版本": "Versiyon", "粤英混合": "Yue-İngilizce Karışık", "粤语": "<PERSON><PERSON>", "终止ASR进程": "ASR işlemini durdur", "终止GPT训练": "GPT eğitimini durdur", "终止SSL提取进程": "SSL çıkarma işlemini durdur", "终止SoVITS训练": "SoVITS eğitimini durdur", "终止一键三连": "Tek tuşla üçlemeyi durdur", "终止文本获取进程": "<PERSON><PERSON> alma i<PERSON><PERSON> durdur", "终止语义token提取进程": "Anlamsal token çıkarma işlemini durdur", "终止语音切割": "<PERSON><PERSON> kesimini durdur", "终止语音降噪进程": "Gürültü azaltma işlemini durdur", "缺少Hubert数据集": "<PERSON>", "缺少语义数据集": "Anlamsal Veri Seti Eksik", "缺少音素数据集": "<PERSON>onem Veri Seti Eksik", "缺少音频数据集": "Ses Veri Seti E<PERSON>", "英文": "İngilizce", "语义token提取进程输出信息": "Anlamsal token çıkarma işlemi çıktı bilgisi", "语速": "Konuşma hızı", "语速调整，高为更快": "Konuşma hızını a<PERSON>, y<PERSON><PERSON><PERSON> daha hızlı", "语音切割进程输出信息": "Ses kesim işlemi çıktı bilgisi", "语音降噪进程输出信息": "Gürültü azaltma işlemi çıktı bilgisi", "请上传3~10秒内参考音频，超过会报错！": "Lütfen 3~10 saniye arasında bir referans ses dosyası yü<PERSON>, aşım durumunda hata verilecektir!", "请上传参考音频": "Lütfen Referans Sesi Yükleyin", "请填入推理文本": "Lütfen Hedef <PERSON>", "请填入正确的List路径": "Lütfen Doğru Liste Yolunu Girin", "请填入正确的音频文件夹路径": "Lütfen Doğru Ses Klasörü Yolunu G<PERSON>n", "请输入有效文本": "Ge<PERSON><PERSON><PERSON> metin girin", "路径不能为空": "Boş Yol Beklenmiyor", "路径错误": "<PERSON>l <PERSON>", "转换": "Dönüş<PERSON>ür", "输入待处理音频文件夹路径": "İşlenecek ses klasörünün yolunu girin:", "输入文件夹路径": "<PERSON><PERSON><PERSON> yolu girin", "输出logs/实验名目录下应有23456开头的文件和文件夹": "Çı<PERSON><PERSON> logs/deney adı dizininde 23456 ile başlayan dosya ve klasörler olmalı", "输出信息": "Çıkış bilgisi", "输出文件夹路径": "Çıktı klasörü yolu", "输出的语音": "Çı<PERSON><PERSON> sesi", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "Eğitimi tamamlanmış ve SoVITS_weights ile GPT_weights altına kaydedilmiş modeli seçin. Varsayılan bir temel modeldir, 5 saniyelik Zero Shot TTS deneyimi için kullanılır.", "降噪结果输出文件夹": "Gürültü azaltma sonuçları çıktı klasörü", "降噪音频文件输入文件夹": "Gürültü azaltma ses dosyaları giriş klasörü", "需要合成的文本": "Sentezlenmesi gereken metin", "需要合成的语种": "Sentezlenmesi gereken dil", "韩文": "<PERSON><PERSON><PERSON>", "韩英混合": "Korece-İngilizce Karışık", "音频加载失败": "<PERSON><PERSON>", "音频自动切分输入路径，可文件可文件夹": "<PERSON>s otomatik b<PERSON><PERSON><PERSON>, dosya veya klasör o<PERSON>bilir", "预训练的GPT模型路径": "<PERSON><PERSON> GPT model yolu", "预训练的SSL模型路径": "<PERSON><PERSON> SSL model yolu", "预训练的SoVITS-D模型路径": "<PERSON>n e<PERSON> SoVITS-D model yolu", "预训练的SoVITS-G模型路径": "<PERSON><PERSON> e<PERSON> SoVITS-G model yolu", "预训练的中文BERT模型路径": "Ön eğ<PERSON>lmiş Çince BERT model yolu"}