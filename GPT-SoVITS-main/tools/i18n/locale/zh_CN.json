{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho:去除延迟效果。Aggressive 比 Normal 去除得更彻底，DeReverb 额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。", "*GPT模型列表": "*GPT模型列表", "*SoVITS模型列表": "*SoVITS模型列表", "*实验/模型名": "*实验/模型名", "*文本标注文件": "*文本标注文件", "*训练集音频文件目录": "*训练集音频文件目录", "*请上传并填写参考信息": "*请上传并填写参考信息", "*请填写需要合成的目标文本和语种模式": "*请填写需要合成的目标文本和语种模式", ".list标注文件的路径": ".list标注文件的路径", ".限制范围越小判别效果越好。": ".限制范围越小判别效果越好。", "0-前置数据集获取工具": "0-前置数据集获取工具", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-UVR5人声伴奏分离&去混响去延迟工具", "0b-语音切分工具": "0b-语音切分工具", "0bb-语音降噪工具": "0bb-语音降噪工具", "0c-中文批量离线ASR工具": "0c-中文批量离线ASR工具", "0d-语音文本校对标注工具": "0d-语音文本校对标注工具", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1A-训练集格式化工具": "1A-训练集格式化工具", "1Aa-文本内容": "1Aa-文本内容", "1Aabc-训练集格式化一键三连": "1Aabc-训练集格式化一键三连", "1Ab-SSL自监督特征提取": "1Ab-SSL自监督特征提取", "1Ac-语义token提取": "1Ac-语义token提取", "1B-微调训练": "1B-微调训练", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。", "1C-推理": "1C-推理", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1、De<PERSON><PERSON>-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-变声", "2、MDX-Net-Dereverb模型挺慢的；": "2、MDX-Net-Dereverb模型挺慢的；", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。", "3、去混响、去延迟模型（by FoxJoy）：": "3、去混响、去延迟模型（by FoxJoy）：", "ASR 模型": "ASR 模型", "ASR 模型尺寸": "ASR 模型尺寸", "ASR 语言设置": "ASR 语言设置", "ASR进程输出信息": "ASR进程输出信息", "GPT模型列表": "GPT模型列表", "GPT训练进程输出信息": "GPT训练进程输出信息", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "GPT采样参数(无参考文本时不要太低。不懂就用默认)：", "GPU卡号,只能填1个整数": "GPU卡号,只能填1个整数", "GPU卡号以-分割，每个卡号一个进程": "GPU卡号以-分割，每个卡号一个进程", "SSL进程输出信息": "SSL进程输出信息", "SoVITS模型列表": "SoVITS模型列表", "SoVITS训练进程输出信息": "SoVITS训练进程输出信息", "TTS推理WebUI进程输出信息": "TTS推理WebUI进程输出信息", "TTS推理进程已关闭": "TTS推理进程已关闭", "TTS推理进程已开启": "TTS推理进程已开启", "UVR5已关闭": "UVR5已关闭", "UVR5已开启": "UVR5已开启", "UVR5进程输出信息": "UVR5进程输出信息", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix:混多少比例归一化后音频进来", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）", "max:归一化后最大值多少": "max:归一化后最大值多少", "max_sil_kept:切完后静音最多留多长": "max_sil_kept:切完后静音最多留多长", "min_interval:最短切割间隔": "min_interval:最短切割间隔", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值", "temperature": "temperature", "threshold:音量小于这个值视作静音的备选切割点": "threshold:音量小于这个值视作静音的备选切割点", "top_k": "top_k", "top_p": "top_p", "一键三连进程输出信息": "一键三连进程输出信息", "不切": "不切", "中文": "中文", "中文教程文档：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e": "中文教程文档：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e", "中英混合": "中英混合", "也可批量输入音频文件, 二选一, 优先读文件夹": "也可批量输入音频文件, 二选一, 优先读文件夹", "人声伴奏分离批量处理， 使用UVR5模型。": "人声伴奏分离批量处理， 使用UVR5模型。", "人声提取激进程度": "人声提取激进程度", "以下文件或文件夹不存在": "以下文件或文件夹不存在", "以下模型不存在:": "以下模型不存在:", "伴奏人声分离&去混响&去回声": "伴奏人声分离&去混响&去回声", "使用无参考文本模式时建议使用微调的GPT，听不清参考音频说的啥(不晓得写啥)可以开。<br>开启后无视填写的参考文本。": "使用无参考文本模式时建议使用微调的GPT，听不清参考音频说的啥(不晓得写啥)可以开。<br>开启后无视填写的参考文本。", "保存频率save_every_epoch": "保存频率save_every_epoch", "关闭TTS推理WebUI": "关闭TTS推理WebUI", "关闭UVR5-WebUI": "关闭UVR5-WebUI", "关闭打标WebUI": "关闭打标WebUI", "凑50字一切": "凑50字一切", "凑四句一切": "凑四句一切", "切分后的子音频的输出根目录": "切分后的子音频的输出根目录", "切割使用的进程数": "切割使用的进程数", "刷新模型路径": "刷新模型路径", "前端处理后的文本(每句):": "前端处理后的文本(每句):", "去混响/去延迟，附：": "去混响/去延迟，附：", "参考音频在3~10秒范围外，请更换！": "参考音频在3~10秒范围外，请更换！", "参考音频的文本": "参考音频的文本", "参考音频的语种": "参考音频的语种", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。", "合成语音": "合成语音", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。", "多语种混合": "多语种混合", "多语种混合(粤语)": "多语种混合(粤语)", "实际输入的参考文本:": "实际输入的参考文本:", "实际输入的目标文本(切句后):": "实际输入的目标文本(切句后):", "实际输入的目标文本(每句):": "实际输入的目标文本(每句):", "实际输入的目标文本:": "实际输入的目标文本:", "导出文件格式": "导出文件格式", "开启GPT训练": "开启GPT训练", "开启SSL提取": "开启SSL提取", "开启SoVITS训练": "开启SoVITS训练", "开启TTS推理WebUI": "开启TTS推理WebUI", "开启UVR5-WebUI": "开启UVR5-WebUI", "开启一键三连": "开启一键三连", "开启打标WebUI": "开启打标WebUI", "开启文本获取": "开启文本获取", "开启无参考文本模式。不填参考文本亦相当于开启。": "开启无参考文本模式。不填参考文本亦相当于开启。", "开启离线批量ASR": "开启离线批量ASR", "开启语义token提取": "开启语义token提取", "开启语音切割": "开启语音切割", "开启语音降噪": "开启语音降噪", "怎么切": "怎么切", "总训练轮数total_epoch": "总训练轮数total_epoch", "总训练轮数total_epoch，不建议太高": "总训练轮数total_epoch，不建议太高", "打标工具WebUI已关闭": "打标工具WebUI已关闭", "打标工具WebUI已开启": "打标工具WebUI已开启", "打标工具进程输出信息": "打标工具进程输出信息", "指定输出主人声文件夹": "指定输出主人声文件夹", "指定输出非主人声文件夹": "指定输出非主人声文件夹", "按中文句号。切": "按中文句号。切", "按标点符号切": "按标点符号切", "按英文句号.切": "按英文句号.切", "数据类型精度": "数据类型精度", "文本模块学习率权重": "文本模块学习率权重", "文本进程输出信息": "文本进程输出信息", "施工中，请静候佳音": "施工中，请静候佳音", "日文": "日文", "日英混合": "日英混合", "是否仅保存最新的ckpt文件以节省硬盘空间": "是否仅保存最新的ckpt文件以节省硬盘空间", "是否在每次保存时间点将最终小模型保存至weights文件夹": "是否在每次保存时间点将最终小模型保存至weights文件夹", "是否开启dpo训练选项(实验性)": "是否开启dpo训练选项(实验性)", "是否直接对上次合成结果调整语速和音色。防止随机性。": "是否直接对上次合成结果调整语速和音色。防止随机性。", "显卡信息": "显卡信息", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.", "模型": "模型", "模型分为三类：": "模型分为三类：", "模型切换": "模型切换", "每张显卡的batch_size": "每张显卡的batch_size", "版本": "版本", "粤英混合": "粤英混合", "粤语": "粤语", "终止ASR进程": "终止ASR进程", "终止GPT训练": "终止GPT训练", "终止SSL提取进程": "终止SSL提取进程", "终止SoVITS训练": "终止SoVITS训练", "终止一键三连": "终止一键三连", "终止文本获取进程": "终止文本获取进程", "终止语义token提取进程": "终止语义token提取进程", "终止语音切割": "终止语音切割", "终止语音降噪进程": "终止语音降噪进程", "缺少Hubert数据集": "缺少Hubert数据集", "缺少语义数据集": "缺少语义数据集", "缺少音素数据集": "缺少音素数据集", "缺少音频数据集": "缺少音频数据集", "英文": "英文", "语义token提取进程输出信息": "语义token提取进程输出信息", "语速": "语速", "语速调整，高为更快": "语速调整，高为更快", "语音切割进程输出信息": "语音切割进程输出信息", "语音降噪进程输出信息": "语音降噪进程输出信息", "请上传3~10秒内参考音频，超过会报错！": "请上传3~10秒内参考音频，超过会报错！", "请上传参考音频": "请上传参考音频", "请填入推理文本": "请填入推理文本", "请填入正确的List路径": "请填入正确的List路径", "请填入正确的音频文件夹路径": "请填入正确的音频文件夹路径", "请输入有效文本": "请输入有效文本", "路径不能为空": "路径不能为空", "路径错误": "路径错误", "转换": "转换", "输入待处理音频文件夹路径": "输入待处理音频文件夹路径", "输入文件夹路径": "输入文件夹路径", "输出logs/实验名目录下应有23456开头的文件和文件夹": "输出logs/实验名目录下应有23456开头的文件和文件夹", "输出信息": "输出信息", "输出文件夹路径": "输出文件夹路径", "输出的语音": "输出的语音", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。", "降噪结果输出文件夹": "降噪结果输出文件夹", "降噪音频文件输入文件夹": "降噪音频文件输入文件夹", "需要合成的文本": "需要合成的文本", "需要合成的语种": "需要合成的语种", "韩文": "韩文", "韩英混合": "韩英混合", "音频加载失败": "音频加载失败", "音频自动切分输入路径，可文件可文件夹": "音频自动切分输入路径，可文件可文件夹", "预训练的GPT模型路径": "预训练的GPT模型路径", "预训练的SSL模型路径": "预训练的SSL模型路径", "预训练的SoVITS-D模型路径": "预训练的SoVITS-D模型路径", "预训练的SoVITS-G模型路径": "预训练的SoVITS-G模型路径", "预训练的中文BERT模型路径": "预训练的中文BERT模型路径"}