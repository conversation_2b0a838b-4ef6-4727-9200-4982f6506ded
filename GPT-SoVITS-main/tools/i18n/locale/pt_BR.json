{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net (onnx_dereverb): É a melhor opção para reverberação de dois canais, mas não pode remover a reverberação de um único canal;", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho:Remove os efeitos de atraso. Aggressive é mais completo que Normal na remoção, DeReverb remove adicionalmente a reverberação, pode remover a reverberação de um canal único, mas não remove completamente a reverberação de placa de alta frequência.", "*GPT模型列表": "*Lista de modelos GPT", "*SoVITS模型列表": "*Lista de modelos Sovits", "*实验/模型名": "*Nome do experimento/modelo", "*文本标注文件": "*Arquivo de marcação de texto", "*训练集音频文件目录": "*Diretório de arquivos de áudio do conjunto de treinamento", "*请上传并填写参考信息": "Por favor, faça o upload e preencha as informações de referência", "*请填写需要合成的目标文本和语种模式": "*Por favor, insira o texto alvo a ser sintetizado e o modo de idioma.", ".list标注文件的路径": "Caminho do arquivo de anotação .list", ".限制范围越小判别效果越好。": "Menos multilinguismo é me<PERSON>hor", "0-前置数据集获取工具": "0- Ferramenta de aquisição de conjunto de dados pré-frontal", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0A-UVR5 separação de voz e acompanhamento instrumental & ferramenta para remover reverberação e atraso", "0b-语音切分工具": "0b- Ferramenta de corte de voz", "0bb-语音降噪工具": "0bb- Ferramenta de redução de ruído de voz", "0c-中文批量离线ASR工具": "0c- Ferramenta chinesa de ASR offline em lote", "0d-语音文本校对标注工具": "0d- Ferramenta de correção e marcação de texto de voz", "1-GPT-SoVITS-TTS": "1-GPT-SOVITS-TTS", "1A-训练集格式化工具": "1A-Ferramenta de formatação de conjunto de dados de treinamento", "1Aa-文本内容": "1AA-Conteúdo do texto", "1Aabc-训练集格式化一键三连": "1AABC-Formatação de conjunto de treinamento em um clique", "1Ab-SSL自监督特征提取": "1AB-Extração de características auto-supervisionadas SSL", "1Ac-语义token提取": "1AC-Extração de token semântico", "1B-微调训练": "1B-Treinamento de ajuste fino", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1ba-Treinamento SoVITS. O arquivo de modelo para compartilhamento é gerado em SOVITS_WEIGHTS", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1BB-Treinamento GPT. O arquivo de modelo para compartilhamento é gerado em GPT_WEIGHTS", "1C-推理": "1C-raciocínio", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1. O tempo de processamento do modelo DeEcho-DeReverb é quase o dobro dos outros dois modelos DeEcho;", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1. Manter a voz: selecione isso para áudio sem harmonia, que preserva melhor a voz principal do que o HP5. Inclui dois modelos, HP2 e HP3; o HP3 pode permitir um pequeno vazamento de acompanhamento, mas preserva a voz principal um pouco melhor do que o HP2;", "2-GPT-SoVITS-变声": "2-gpt-sovits-mudança de voz", "2、MDX-Net-Dereverb模型挺慢的；": "2. O modelo MDX-Net-Dereverb é bastante lento;", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2. <PERSON><PERSON> apenas a voz principal: selecione isso para áudio com harmonia, pode haver uma redução na voz principal. Inclui um modelo HP5;", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3. A configuração mais limpa recomendada é usar primeiro o MDX-Net e depois o DeEcho-Aggressive.", "3、去混响、去延迟模型（by FoxJoy）：": "3. Modelo de remoção de reverberação e atraso (por FoxJoy):", "ASR 模型": "Modelo ASR", "ASR 模型尺寸": "Tamanho do modelo ASR", "ASR 语言设置": "Configurações de idioma do ASR", "ASR进程输出信息": "Informações de saída do processo ASR", "GPT模型列表": "Lista de modelos GPT", "GPT训练进程输出信息": "Informações de saída do processo de treinamento GPT", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "Parâmetros de amostragem do GPT (não muito baixos quando não houver texto de referência. Use o padrão se não tiver certeza):", "GPU卡号,只能填1个整数": "Número da placa de vídeo, só é possível preencher com um número inteiro", "GPU卡号以-分割，每个卡号一个进程": "Número da placa de vídeo dividido por-, cada número de placa é um processo", "SSL进程输出信息": "Informações de saída do processo SSL", "SoVITS模型列表": "Lista de modelos SoVITS", "SoVITS训练进程输出信息": "Informações de saída do processo de treinamento SoVITS", "TTS推理WebUI进程输出信息": "Informações de saída do processo webui de raciocínio TTS", "TTS推理进程已关闭": "O processo de inferência TTS foi desativado", "TTS推理进程已开启": "O processo de inferência TTS foi iniciado", "UVR5已关闭": "UVR5 está desativado", "UVR5已开启": "UVR5 está ativado", "UVR5进程输出信息": "Informações de saída do processo UVR5", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix: Em que proporção o áudio normalizado é misturado de volta", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "HOP_SIZE: Como calcular a curva de volume, quanto menor a precisão, maior a quantidade de cálculos (não significa que quanto maior a precisão, melhor o efeito)", "max:归一化后最大值多少": "MAX: Qual é o valor máximo após a normalização?", "max_sil_kept:切完后静音最多留多长": "max_sil_kept: <PERSON><PERSON><PERSON> de cortar, por quanto tempo no máximo o silêncio é mantido", "min_interval:最短切割间隔": "min_interval: O intervalo de corte mínimo", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length: Comprimento mínimo de cada segmento. Se o primeiro segmento for muito curto, ele será unido aos segmentos seguintes até exceder este valor", "temperature": "temperatura", "threshold:音量小于这个值视作静音的备选切割点": "Limiar: O volume menor que este valor é considerado como um ponto de corte mudo alternativo", "top_k": "top_k", "top_p": "top_p", "一键三连进程输出信息": "Informações de saída do processo de um clique", "不切": "Não dividir", "中文": "<PERSON><PERSON><PERSON>", "中文教程文档：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e": "Documentação do tutorial em chinês：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e", "中英混合": "Mistura de Chinês e Inglês", "也可批量输入音频文件, 二选一, 优先读文件夹": "Também é possível inserir arquivos de áudio em lote; escolha uma opção, preferencialmente leia a pasta.", "人声伴奏分离批量处理， 使用UVR5模型。": "Processamento em lote de separação de voz e acompanhamento, usando o modelo UVR5.", "人声提取激进程度": "Grau de agressividade da extração de voz", "以下文件或文件夹不存在": "Nenhum Arquivo ou Pasta Encontrado", "以下模型不存在:": "<PERSON>en<PERSON>o <PERSON>:", "伴奏人声分离&去混响&去回声": "Separação de acompanhamento e voz & remoção de reverberação & remoção de eco", "使用无参考文本模式时建议使用微调的GPT，听不清参考音频说的啥(不晓得写啥)可以开。<br>开启后无视填写的参考文本。": "Ao usar o modo sem texto de referência, recomenda-se usar um GPT ajustado. Se não conseguir ouvir claramente o áudio de referência (não sabe o que escrever), você pode ativar o modo e ignorar o texto de referência fornecido.", "保存频率save_every_epoch": "Frequência de salvamento save_every_epoch", "关闭TTS推理WebUI": "Fechar TTS Inference WebUI", "关闭UVR5-WebUI": "Fechar <PERSON>R5-WebUI", "关闭打标WebUI": "Fechar Labeling WebUI", "凑50字一切": "Complete com 50 caracteres", "凑四句一切": "Complete com quatro frases", "切分后的子音频的输出根目录": "Diretório raiz de saída do sub-áudio após o corte", "切割使用的进程数": "Número de processos para corte", "刷新模型路径": "Atualizar caminho do modelo", "前端处理后的文本(每句):": "Texto após processamento front-end (por frase):", "去混响/去延迟，附：": "Remoção de reverberação/remoção de atraso, anexo:", "参考音频在3~10秒范围外，请更换！": "O áudio de referência está fora do intervalo de 3 a 10 segundos. Por favor, substitua!", "参考音频的文本": "Texto do áudio de referência", "参考音频的语种": "Idioma do áudio de referência", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "Opcional: Faça upload de vários arquivos de áudio de referência arrastando e soltando-os (recomendado que sejam do mesmo gênero) e faça uma média dos seus tons. Se essa opção for deixada em branco, o tom será controlado pelo único áudio de referência à esquerda. Se estiver ajustando o modelo, é recomendado que todos os arquivos de áudio de referência tenham tons dentro do conjunto de treinamento de ajuste; o modelo pré-treinado pode ser ignorado.", "合成语音": "<PERSON><PERSON>", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "Exemplo de formato de caminho de pasta válido: E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例 (copie do endereço da barra do gerenciador de arquivos).", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "Preencha o diretório onde os áudios cortados estão localizados! O caminho completo dos arquivos de áudio lidos = este diretório - concatenação com o nome do arquivo de forma correspondente no arquivo .list (não o caminho completo). Se deixar em branco, use o caminho absoluto no arquivo .list.", "多语种混合": "Mistura de múltiplos idiomas", "多语种混合(粤语)": "Mistura Multilíngue (Yue)", "实际输入的参考文本:": "Texto de referência realmente inserido:", "实际输入的目标文本(切句后):": "Texto alvo realmente inserido (após divisão de frases):", "实际输入的目标文本(每句):": "Texto alvo realmente inserido (por frase):", "实际输入的目标文本:": "Texto alvo realmente inserido:", "导出文件格式": "Formato de arquivo de exportação", "开启GPT训练": "Ativar treinamento GPT", "开启SSL提取": "Ativar extração SSL", "开启SoVITS训练": "Ativar treinamento SoVITS", "开启TTS推理WebUI": "Abrir TTS Inference WebUI", "开启UVR5-WebUI": "Abrir UVR5-WebUI", "开启一键三连": "Ativar um clique", "开启打标WebUI": "Abrir Labeling WebUI", "开启文本获取": "Ativar obtenção de texto", "开启无参考文本模式。不填参考文本亦相当于开启。": "Ativar o modo sem texto de referência. Não preencher o texto de referência também equivale a ativar.", "开启离线批量ASR": "Ativar ASR offline em lote", "开启语义token提取": "Ativar extração de token semântico", "开启语音切割": "Ativar corte de voz", "开启语音降噪": "Ativar redução de ruído de voz", "怎么切": "Como cortar", "总训练轮数total_epoch": "Total de epoch de treinamento", "总训练轮数total_epoch，不建议太高": "Total de epoch de treinamento, não é recomendável um valor muito alto", "打标工具WebUI已关闭": "A ferramenta de marcação WebUI foi desativado", "打标工具WebUI已开启": "A ferramenta de marcação WebUI está ativada", "打标工具进程输出信息": "Informações de saída do processo da ferramenta de marcação", "指定输出主人声文件夹": "Especificar a pasta de saída da voz principal", "指定输出非主人声文件夹": "Especificar a pasta de saída da voz secundária", "按中文句号。切": "Dividir por ponto final chinês", "按标点符号切": "Dividir por sinais de pontuação", "按英文句号.切": "Dividir por ponto final em inglês", "数据类型精度": "precisão do tipo de dado", "文本模块学习率权重": "Weight da taxa de aprendizado do módulo de texto", "文本进程输出信息": "Informações de saída do processo de texto", "施工中，请静候佳音": "Em construção, por favor, aguarde por um bom som", "日文": "<PERSON><PERSON><PERSON><PERSON>", "日英混合": "Mistura de Japonês e Inglês", "是否仅保存最新的ckpt文件以节省硬盘空间": "Se deve salvar apenas o último arquivo CKPT para economizar espaço em disco", "是否在每次保存时间点将最终小模型保存至weights文件夹": "Se deve salvar o modelo pequeno final na pasta Weights em cada ponto de salvamento de tempo", "是否开启dpo训练选项(实验性)": "Se deseja ativar a opção de treinamento DPO (experimental)", "是否直接对上次合成结果调整语速和音色。防止随机性。": "Ajuste a velocidade da fala e o tom do último resultado de síntese para evitar aleatoriedade.", "显卡信息": "Informações da placa de vídeo", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "Este software é de código aberto sob a licença MIT. O autor não tem controle sobre o software. Aqueles que usam o software e difundem os sons exportados pelo software são totalmente responsáveis. <br>Se você não concorda com esta cláusula, não pode usar ou citar nenhum código e arquivo dentro do pacote de software. Consulte o diretório raiz <b>LICENSE</b> para mais detalhes.<br><br> Traduzido por <PERSON>", "模型": "<PERSON><PERSON>", "模型分为三类：": "Modelos dividem-se em três categorias:", "模型切换": "Troca de modelo", "每张显卡的batch_size": "Tamanho do lote de cada placa de vídeo", "版本": "Vers<PERSON>", "粤英混合": "<PERSON><PERSON><PERSON><PERSON>", "粤语": "<PERSON><PERSON>", "终止ASR进程": "Encerrar processo ASR", "终止GPT训练": "Encerrar treinamento GPT", "终止SSL提取进程": "Encerrar processo de extração SSL", "终止SoVITS训练": "Encerrar treinamento SoVITS", "终止一键三连": "Encerrar um clique", "终止文本获取进程": "Encerrar processo de obtenção de texto", "终止语义token提取进程": "Encerrar processo de extração de token semântico", "终止语音切割": "Encerrar corte de voz", "终止语音降噪进程": "Encerrar o processo de redução de ruído de voz", "缺少Hubert数据集": "Conjunto de Dados <PERSON>", "缺少语义数据集": "Conjunto de Dados Semânticos Ausente", "缺少音素数据集": "Conjunto de Dados de Fonemas Ausente", "缺少音频数据集": "Conjunto de Dados de Áudio Ausente", "英文": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "语义token提取进程输出信息": "Informações de saída do processo de extração de token semântico", "语速": "Velocidade da fala", "语速调整，高为更快": "Ajustar a velocidade da fala, mais alta para mais rápido", "语音切割进程输出信息": "Informações de saída do processo de corte de voz", "语音降噪进程输出信息": "Informações de saída do processo de redução de ruído de voz", "请上传3~10秒内参考音频，超过会报错！": "Por favor, faça upload de um áudio de referência com duração entre 3 e 10 segundos. Áudios fora dessa faixa causarão erro!", "请上传参考音频": "<PERSON><PERSON> <PERSON>, Carregue o Áudio de Referência", "请填入推理文本": "<PERSON><PERSON>, Preencha o Texto de Inferência", "请填入正确的List路径": "<PERSON><PERSON>, Insira o Caminho Correto da Lista", "请填入正确的音频文件夹路径": "<PERSON><PERSON>, Insira o Caminho Correto da Pasta de Áudio", "请输入有效文本": "Por favor, insira um texto válido", "路径不能为空": "<PERSON><PERSON><PERSON> Caminho Não Vazio", "路径错误": "<PERSON><PERSON>", "转换": "Converter", "输入待处理音频文件夹路径": "Caminho da pasta de arquivos de áudio a ser processados", "输入文件夹路径": "<PERSON><PERSON><PERSON> da pasta de entrada", "输出logs/实验名目录下应有23456开头的文件和文件夹": "Logs de saída/deve haver arquivos e pastas começando com 23456 no diretório do nome do experimento", "输出信息": "Informações de saída", "输出文件夹路径": "<PERSON><PERSON><PERSON> da pasta de saída", "输出的语音": "Áudio de saída", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "Selecione os modelos armazenados em Sovits_weights e GPT_WEIGHTS. O padrão é o modelo inferior, experiência para 5 segundos de Zero Shot TTS", "降噪结果输出文件夹": "Pasta de saída dos resultados de redução de ruído", "降噪音频文件输入文件夹": "Pasta de entrada dos arquivos de áudio para redução de ruído", "需要合成的文本": "Texto a ser sintetizado", "需要合成的语种": "Idioma a ser sintetizado", "韩文": "<PERSON><PERSON>", "韩英混合": "Mistura <PERSON>ano-Inglês", "音频加载失败": "Falha ao Carregar o Áudio", "音频自动切分输入路径，可文件可文件夹": "Caminho de entrada automático de corte de áudio, pode ser um arquivo ou uma pasta", "预训练的GPT模型路径": "Caminho do modelo GPT pre-train", "预训练的SSL模型路径": "Caminho do modelo SSL pre-train", "预训练的SoVITS-D模型路径": "Caminho do modelo SoVITS-D pre-train", "预训练的SoVITS-G模型路径": "Caminho do modelo SoVITS-G pre-train", "预训练的中文BERT模型路径": "Caminho do modelo BERT chinês pre-train"}