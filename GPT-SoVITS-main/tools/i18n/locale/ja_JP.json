{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net(onnx_dereverb):二重チャンネルのリバーブに最適な選択ですが、単一チャンネルのリバーブは除去できません；", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho:遅延効果を除去します。AggressiveはNormalよりも徹底的に除去し、DeReverbは追加でリバーブを除去し、モノラルリバーブを除去できますが、高周波数のプレートリバーブは完全には除去できません。", "*GPT模型列表": "*GPTモデルリスト", "*SoVITS模型列表": "*SoVITSモデルリスト", "*实验/模型名": "*実験/モデル名", "*文本标注文件": "*テキスト注釈ファイル", "*训练集音频文件目录": "*トレーニングデータのオーディオファイルディレクトリ", "*请上传并填写参考信息": "*参照情報をアップロードして記入してください", "*请填写需要合成的目标文本和语种模式": "*合成対象テキストと言語モードを入力してください", ".list标注文件的路径": ".listアノテーションファイルのパス", ".限制范围越小判别效果越好。": "多言語対応を減らした方が良い", "0-前置数据集获取工具": "0-データセット取得ツールの事前処理", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-UVR5ボーカルアカンパニメント分離＆リバーブおよびディレイ除去ツール", "0b-语音切分工具": "0b-音声分割ツール", "0bb-语音降噪工具": "0bb-音声ノイズ除去ツール", "0c-中文批量离线ASR工具": "0c-中国語バッチオフラインASRツール", "0d-语音文本校对标注工具": "0d-音声テキストの校正アノテーションツール", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1A-训练集格式化工具": "1A-トレーニングデータのフォーマットツール", "1Aa-文本内容": "1Aa-テキストの内容", "1Aabc-训练集格式化一键三连": "1Aabc-トレーニングデータのフォーマットワンクリック三連", "1Ab-SSL自监督特征提取": "1Ab-SSLセルフスーパーバイズ特徴抽出", "1Ac-语义token提取": "1Ac-セマンティックトークン抽出", "1B-微调训练": "1B-ファインチューニングトレーニング", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-SoVITSトレーニング。共有用のモデルファイルはSoVITS_weightsディレクトリに出力されます。", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-GPTトレーニング。共有用のモデルファイルはGPT_weightsディレクトリに出力されます。", "1C-推理": "1C-推論", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1、DeEcho-DeReverbモデルの処理時間は、他の2つのDeEchoモデルのほぼ2倍です；", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1、主音を保持: ハーモニーなしの音声にはこのオプションを選択し、HP5よりも主音の保持が優れています。HP2とHP3の2つのモデルが内蔵されており、HP3はわずかに伴奏を漏らす可能性がありますが、HP2よりも主音の保持がわずかに良いです；", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-ボイスチェンジャー", "2、MDX-Net-Dereverb模型挺慢的；": "2、MDX-Net-Dereverbモデルはかなり遅いです；", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2、主音のみを保持: ハーモニー付きの音声にはこのオプションを選択し、主音が弱くなる可能性があります。HP5モデルが1つ内蔵されています；", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3、最もクリーンな設定は、MDX-Netの後にDeEcho-Aggressiveを使用することをお勧めします。", "3、去混响、去延迟模型（by FoxJoy）：": "3、リバーブ除去と遅延除去モデル（by FoxJoy）：", "ASR 模型": "ASR モデル", "ASR 模型尺寸": "ASRモデルサイズ", "ASR 语言设置": "ASR 言語設定", "ASR进程输出信息": "ASRプロセスの出力情報", "GPT模型列表": "GPTモデルリスト", "GPT训练进程输出信息": "GPTトレーニングプロセスの出力情報", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "GPT サンプリングパラメーター（参照テキストがない場合はあまり低くしないでください。わからない場合はデフォルトを使用してください）：", "GPU卡号,只能填1个整数": "GPU番号、1つの整数しか入力できません", "GPU卡号以-分割，每个卡号一个进程": "GPUカード番号はハイフンで区切り、各カード番号ごとに1つのプロセスが実行されます", "SSL进程输出信息": "SSLプロセスの出力情報", "SoVITS模型列表": "SoVITSモデルリスト", "SoVITS训练进程输出信息": "SoVITSトレーニングプロセスの出力情報", "TTS推理WebUI进程输出信息": "TTS推論WebUIプロセスの出力情報", "TTS推理进程已关闭": "TTS推論プロセスが終了しました", "TTS推理进程已开启": "TTS推論プロセスが開始されました", "UVR5已关闭": "UVR5がオフになっています", "UVR5已开启": "UVR5がオンになっています", "UVR5进程输出信息": "UVR5プロセスの出力情報", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix：正規化後のオーディオが入る割合", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size: 音量曲線の計算方法、小さいほど精度が高くなりますが、計算量が増加します（精度が高いほど必ずしも効果が良いわけではありません）", "max:归一化后最大值多少": "max：正規化後の最大値", "max_sil_kept:切完后静音最多留多长": "max_sil_kept：切り終えた後、最大でどれだけ静かにするか", "min_interval:最短切割间隔": "min_interval：最短カット間隔", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length：各セグメントの最小長さ。最初のセグメントが短すぎる場合、連続して後続のセグメントに接続され、この値を超えるまで続きます。", "temperature": "temperature", "threshold:音量小于这个值视作静音的备选切割点": "閾値：この値未満の音量は静音と見なされ、代替のカットポイントとして扱われます", "top_k": "top_k", "top_p": "top_p", "一键三连进程输出信息": "ワンクリック三連プロセスの出力情報", "不切": "切らない", "中文": "中国語", "中文教程文档：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e": "中国語チュートリアルドキュメント：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e", "中英混合": "中英混合", "也可批量输入音频文件, 二选一, 优先读文件夹": "複数のオーディオファイルもインポートできます。フォルダパスが存在する場合、この入力は無視されます。", "人声伴奏分离批量处理， 使用UVR5模型。": "人声と伴奏の分離をバッチ処理で行い、UVR5モデルを使用します。", "人声提取激进程度": "人声抽出の積極性", "以下文件或文件夹不存在": "そのようなファイルまたはフォルダは存在しません", "以下模型不存在:": "モデルが存在しません:", "伴奏人声分离&去混响&去回声": "ボーカル/伴奏の分離と残響の除去", "使用无参考文本模式时建议使用微调的GPT，听不清参考音频说的啥(不晓得写啥)可以开。<br>开启后无视填写的参考文本。": "参考テキストなしモードを使用する場合は、微調整されたGPTの使用をお勧めします。参考音声が聞き取れない場合（何を書けば良いかわからない場合）は、有効にすると、入力した参考テキストを無視します。", "保存频率save_every_epoch": "保存頻度save_every_epoch", "关闭TTS推理WebUI": "TTS Inference WebUIを閉じる", "关闭UVR5-WebUI": "UVR5-WebUIを閉じる", "关闭打标WebUI": "ラベリングWebUIを閉じる", "凑50字一切": "50文字ずつカット", "凑四句一切": "4つの文で埋める", "切分后的子音频的输出根目录": "分割後のサブオーディオの出力ルートディレクトリ", "切割使用的进程数": "分割に使用されるプロセス数", "刷新模型路径": "モデルのパスを更新", "前端处理后的文本(每句):": "フロントエンド処理後のテキスト（文ごと）:", "去混响/去延迟，附：": "残響除去/遅延除去、附：", "参考音频在3~10秒范围外，请更换！": "参照音声が3～10秒の範囲外です。別の音声に変更してください！", "参考音频的文本": "参照オーディオのテキスト", "参考音频的语种": "参照オーディオの言語", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "オプション：複数の参照オーディオファイルをドラッグ＆ドロップしてアップロードし、それらのトーンを平均化します（同性推奨）。このオプションを空白のままにした場合、トーンは左側の単一の参照オーディオによって制御されます。モデルを微調整する場合、すべての参照オーディオファイルが微調整のトレーニングセット内のトーンを持つことをお勧めします。プリトレーニングモデルは無視しても構いません。", "合成语音": "推論を開始", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "適切なフォルダパスの例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华テストサンプル（ファイルマネージャのアドレスバーからコピーしてください）。", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "切断後の音声ファイルが格納されているディレクトリを入力してください！読み取り対象の音声ファイルの完全パス = このディレクトリ - 結合 - listファイル内の波形に対応するファイル名（完全パスではありません）。空白の場合、.listファイル内の絶対完全パスを使用します。", "多语种混合": "多言語混合", "多语种混合(粤语)": "多言語混合(粤語)", "实际输入的参考文本:": "実際に入力された参照テキスト：", "实际输入的目标文本(切句后):": "実際に入力された目標テキスト（文分割後）：", "实际输入的目标文本(每句):": "実際に入力された目標テキスト（文ごと）：", "实际输入的目标文本:": "実際に入力された目標テキスト：", "导出文件格式": "エクスポートファイル形式", "开启GPT训练": "GPTトレーニングを開始", "开启SSL提取": "SSL抽出を開始", "开启SoVITS训练": "SoVITSトレーニングを開始", "开启TTS推理WebUI": "TTS Inference WebUIを開く", "开启UVR5-WebUI": "UVR5-WebUIを開く", "开启一键三连": "ワンクリック三連を開始", "开启打标WebUI": "ラベリングWebUIを開く", "开启文本获取": "テキストの取得を開始", "开启无参考文本模式。不填参考文本亦相当于开启。": "参照テキストなしモードを有効にします。参照テキストを入力しない場合も同様に有効になります。", "开启离线批量ASR": "オフラインバッチASRを開始", "开启语义token提取": "セマンティックトークン抽出を開始", "开启语音切割": "音声の分割を開始", "开启语音降噪": "音声ノイズ除去を有効にする", "怎么切": "どうやって切るか", "总训练轮数total_epoch": "総トレーニングエポック数total_epoch", "总训练轮数total_epoch，不建议太高": "総トレーニングエポック数total_epoch、高すぎないようにお勧めします", "打标工具WebUI已关闭": "校正ツールWebUIが終了しました", "打标工具WebUI已开启": "校正ツールWebUIが開始されました", "打标工具进程输出信息": "アノテーションツールプロセスの出力情報", "指定输出主人声文件夹": "ボーカルの出力フォルダを指定:", "指定输出非主人声文件夹": "伴奏の出力フォルダを指定:", "按中文句号。切": "中国語の句点でカット", "按标点符号切": "句読点で分割", "按英文句号.切": "英文のピリオドで切ってください", "数据类型精度": "データ型の精度", "文本模块学习率权重": "テキストモジュールの学習率の重み", "文本进程输出信息": "テキストプロセスの出力情報", "施工中，请静候佳音": "施工中、お待ちください", "日文": "日本語", "日英混合": "日英混合", "是否仅保存最新的ckpt文件以节省硬盘空间": "最新のckptファイルのみを保存してディスクスペースを節約するかどうか", "是否在每次保存时间点将最终小模型保存至weights文件夹": "各保存時間点で最終的な小さなモデルをweightsフォルダに保存するかどうか", "是否开启dpo训练选项(实验性)": "DPOトレーニングオプションを有効にするかどうか（実験的）", "是否直接对上次合成结果调整语速和音色。防止随机性。": "ランダム性を防ぐために、前回の合成結果のスピーチ速度とトーンを調整します。", "显卡信息": "グラフィックカード情報", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "このソフトウェアはMITライセンスでオープンソース化されており、作者はソフトウェアに対して一切の制御権を持っていません。ソフトウェアを使用する者、ソフトウェアから導出される音声を広める者は、自己責任で行ってください。<br>この条件を認めない場合、ソフトウェアパッケージ内の任意のコードやファイルを使用または引用することはできません。詳細はルートディレクトリの<b>LICENSE</b>を参照してください。", "模型": "モデル", "模型分为三类：": "モデルは3種類に分かれています：", "模型切换": "モデル切り替え", "每张显卡的batch_size": "各グラフィックカードのバッチサイズ", "版本": "バージョン", "粤英混合": "粤英混合", "粤语": "粤語", "终止ASR进程": "ASRプロセスを停止", "终止GPT训练": "GPTトレーニングを停止", "终止SSL提取进程": "SSL抽出プロセスを停止", "终止SoVITS训练": "SoVITSトレーニングを停止", "终止一键三连": "ワンクリック三連を停止", "终止文本获取进程": "テキスト取得プロセスを停止", "终止语义token提取进程": "セマンティックトークン抽出プロセスを停止", "终止语音切割": "音声の分割を停止", "终止语音降噪进程": "音声ノイズ除去プロセスを終了する", "缺少Hubert数据集": "Hubertデータセットが欠落しています", "缺少语义数据集": "セマンティクスデータセットが欠落しています", "缺少音素数据集": "音素データセットが欠落しています", "缺少音频数据集": "オーディオデータセットが欠落しています", "英文": "英語", "语义token提取进程输出信息": "セマンティックトークン抽出プロセスの出力情報", "语速": "話速", "语速调整，高为更快": "話速調整、高いほど速く", "语音切割进程输出信息": "音声分割プロセスの出力情報", "语音降噪进程输出信息": "音声ノイズ除去プロセスの出力情報", "请上传3~10秒内参考音频，超过会报错！": "3～10秒以内の参照音声をアップロードしてください。それを超えるとエラーが発生します！", "请上传参考音频": "リファレンスオーディオをアップロードしてください", "请填入推理文本": "ターゲットテキストを入力してください", "请填入正确的List路径": "正しいリストパスを入力してください", "请填入正确的音频文件夹路径": "正しいオーディオフォルダパスを入力してください", "请输入有效文本": "有効なテキストを入力してください", "路径不能为空": "空のパスは予期されていません", "路径错误": "パスエラー", "转换": "変換", "输入待处理音频文件夹路径": "処理するオーディオフォルダのパスを入力してください:", "输入文件夹路径": "入力フォルダのパス", "输出logs/实验名目录下应有23456开头的文件和文件夹": "logs/実験名ディレクトリには23456で始まるファイルとフォルダが含まれている必要があります", "输出信息": "出力情報", "输出文件夹路径": "出力フォルダのパス", "输出的语音": "推論結果", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "SoVITS_weightsおよびGPT_weightsに保存されたモデルを選択します。デフォルトのものはプレトレインであり、ゼロショットTTSを体験できます。", "降噪结果输出文件夹": "ノイズ除去結果出力フォルダ", "降噪音频文件输入文件夹": "ノイズ除去音声ファイル入力フォルダ", "需要合成的文本": "推論テキスト", "需要合成的语种": "推論テキストの言語", "韩文": "韓国語", "韩英混合": "韓英混合", "音频加载失败": "音声の読み込みに失敗しました", "音频自动切分输入路径，可文件可文件夹": "オーディオの自動分割入力パス、ファイルまたはフォルダを指定できます", "预训练的GPT模型路径": "事前にトレーニングされたGPTモデルのパス", "预训练的SSL模型路径": "事前にトレーニングされたSSLモデルのパス", "预训练的SoVITS-D模型路径": "事前にトレーニングされたSoVITS-Dモデルのパス", "预训练的SoVITS-G模型路径": "事前にトレーニングされたSoVITS-Gモデルのパス", "预训练的中文BERT模型路径": "事前にトレーニングされた中文BERTモデルのパス"}