{"(1)MDX-Net(onnx_dereverb):对于双通道混响是最好的选择，不能去除单通道混响；": "(1)MDX-Net(onnx_dereverb):對於雙通道混響是最好的選擇，不能去除單通道混響；", "(234)DeEcho:去除延迟效果。Aggressive比Normal去除得更彻底，DeReverb额外去除混响，可去除单声道混响，但是对高频重的板式混响去不干净。": "(234)DeEcho: Aggressive 比 Normal 去除得更徹底，DeReverb 額外去除混響，可去除單聲道混響，但是對高頻重的板式混響去不乾淨。", "*GPT模型列表": "*GPT模型列表", "*SoVITS模型列表": "*SoVITS模型列表", "*实验/模型名": "*實驗/模型名", "*文本标注文件": "*文本標註文件", "*训练集音频文件目录": "*訓練集音頻文件目錄", "*请上传并填写参考信息": "*請上傳並填寫參考信息", "*请填写需要合成的目标文本和语种模式": "請填寫需要合成的目標文本和語言模式", ".list标注文件的路径": ".list標註文件的路徑", ".限制范围越小判别效果越好。": ".限制范围越小判别效果越好。", "0-前置数据集获取工具": "0-前置數據集獲取工具", "0a-UVR5人声伴奏分离&去混响去延迟工具": "0a-UVR5人聲伴奏分離&去混響去延遲工具", "0b-语音切分工具": "0b-語音切分工具", "0bb-语音降噪工具": "0bb-語音降噪工具", "0c-中文批量离线ASR工具": "0c-中文批量離線ASR工具", "0d-语音文本校对标注工具": "0d-語音文本校對標註工具", "1-GPT-SoVITS-TTS": "1-GPT-SoVITS-TTS", "1A-训练集格式化工具": "1A-訓練集格式化工具", "1Aa-文本内容": "1Aa-文本內容", "1Aabc-训练集格式化一键三连": "1Aabc-訓練集格式化一鍵三連", "1Ab-SSL自监督特征提取": "1Ab-SSL自監督特徵提取", "1Ac-语义token提取": "1Ac-語義token提取", "1B-微调训练": "1B-微調訓練", "1Ba-SoVITS训练。用于分享的模型文件输出在SoVITS_weights下。": "1Ba-SoVITS訓練。用於分享的模型文件輸出在SoVITS_weights下。", "1Bb-GPT训练。用于分享的模型文件输出在GPT_weights下。": "1Bb-GPT訓練。用於分享的模型文件輸出在GPT_weights下。", "1C-推理": "1C-推理", "1、DeEcho-DeReverb模型的耗时是另外2个DeEcho模型的接近2倍；": "1、DeEcho-DeReverb 模型的耗時是另外兩個 DeEcho 模型的接近兩倍；", "1、保留人声：不带和声的音频选这个，对主人声保留比HP5更好。内置HP2和HP3两个模型，HP3可能轻微漏伴奏但对主人声保留比HP2稍微好一丁点；": "1、保留人聲：不帶和聲的音頻選這個，對主人聲保留比HP5更好。內置HP2和HP3兩個模型，HP3可能輕微漏伴奏但對主人聲保留比HP2稍微好一丁點；", "2-GPT-SoVITS-变声": "2-GPT-SoVITS-變聲", "2、MDX-Net-Dereverb模型挺慢的；": "2、MDX-Net-Dereverb 模型的處理時間挺慢的；", "2、仅保留主人声：带和声的音频选这个，对主人声可能有削弱。内置HP5一个模型；": "2、僅保留主人聲：帶和聲的音頻選這個，對主人聲可能有削弱。內置HP5一個模型；", "3、个人推荐的最干净的配置是先MDX-Net再DeEcho-Aggressive。": "3、個人推薦的最乾淨的配置是先 MDX-Net 再 DeEcho-Aggressive。", "3、去混响、去延迟模型（by FoxJoy）：": "3、去混響、去延遲模型（by FoxJoy）：", "ASR 模型": "ASR 模型", "ASR 模型尺寸": "ASR 模型尺寸", "ASR 语言设置": "ASR 語言設定", "ASR进程输出信息": "ASR進程輸出資訊", "GPT模型列表": "GPT模型列表", "GPT训练进程输出信息": "GPT訓練進程輸出資訊", "GPT采样参数(无参考文本时不要太低。不懂就用默认)：": "GPT 采样参数（无参考文本时不要太低。不懂就用默认）：", "GPU卡号,只能填1个整数": "GPU卡號,只能填1個整數", "GPU卡号以-分割，每个卡号一个进程": "GPU卡號以-分割，每個卡號一個進程", "SSL进程输出信息": "SSL進程輸出資訊", "SoVITS模型列表": "SoVITS模型列表", "SoVITS训练进程输出信息": "SoVITS訓練進程輸出資訊", "TTS推理WebUI进程输出信息": "TTS推理WebUI進程輸出資訊", "TTS推理进程已关闭": "TTS推理進程已關閉", "TTS推理进程已开启": "TTS推理進程已開啟", "UVR5已关闭": "UVR5已關閉", "UVR5已开启": "UVR5已開啟", "UVR5进程输出信息": "UVR5進程輸出資訊", "alpha_mix:混多少比例归一化后音频进来": "alpha_mix:混多少比例歸一化後音頻進來", "hop_size:怎么算音量曲线，越小精度越大计算量越高（不是精度越大效果越好）": "hop_size:怎麼算音量曲線，越小精度越大計算量越高（不是精度越大效果越好）", "max:归一化后最大值多少": "max:歸一化後最大值多少", "max_sil_kept:切完后静音最多留多长": "max_sil_kept:切完後靜音最多留多長", "min_interval:最短切割间隔": "min_interval:最短切割間隔", "min_length:每段最小多长，如果第一段太短一直和后面段连起来直到超过这个值": "min_length:每段最小多長，如果第一段太短一直和後面段連起來直到超過這個值", "temperature": "temperature", "threshold:音量小于这个值视作静音的备选切割点": "threshold:音量小於這個值視作靜音的備選切割點", "top_k": "top_k", "top_p": "top_p", "一键三连进程输出信息": "一鍵三連進程輸出資訊", "不切": "不切", "中文": "中文", "中文教程文档：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e": "中文教程文檔：https://www.yuque.com/baicaigongchang1145haoyuangong/ib3g1e", "中英混合": "中英混合", "也可批量输入音频文件, 二选一, 优先读文件夹": "也可批量输入音频文件, 二选一, 优先读文件夹", "人声伴奏分离批量处理， 使用UVR5模型。": "人聲伴奏分離批量處理， 使用UVR5模型。", "人声提取激进程度": "人聲提取激進程度", "以下文件或文件夹不存在": "沒有此文件或文件夾", "以下模型不存在:": "以下模型不存在", "伴奏人声分离&去混响&去回声": "伴奏人聲分離&去混響&去回聲", "使用无参考文本模式时建议使用微调的GPT，听不清参考音频说的啥(不晓得写啥)可以开。<br>开启后无视填写的参考文本。": "使用無參考文本模式時建議使用微調的GPT，聽不清參考音頻說的啥(不曉得寫啥)可以開，開啟後無視填寫的參考文本。", "保存频率save_every_epoch": "保存頻率save_every_epoch", "关闭TTS推理WebUI": "關閉 TTS Inference WebUI", "关闭UVR5-WebUI": "關閉 UVR5-WebUI", "关闭打标WebUI": "關閉 Labeling WebUI", "凑50字一切": "湊50字一切", "凑四句一切": "湊四句一切", "切分后的子音频的输出根目录": "切分後的子音頻的輸出根目錄", "切割使用的进程数": "切割使用的進程數", "刷新模型路径": "刷新模型路徑", "前端处理后的文本(每句):": "前端處理後的文本(每句):", "去混响/去延迟，附：": "去混響/去延遲，附：", "参考音频在3~10秒范围外，请更换！": "參考音頻在3~10秒範圍外，請更換！", "参考音频的文本": "參考音頻的文本", "参考音频的语种": "參考音頻的語種", "可选项：通过拖拽多个文件上传多个参考音频（建议同性），平均融合他们的音色。如不填写此项，音色由左侧单个参考音频控制。如是微调模型，建议参考音频全部在微调训练集音色内，底模不用管。": "可選項：通過拖曳多個文件上傳多個參考音頻（建議同性），平均融合他們的音色。如不填寫此項，音色由左側單個參考音頻控制。如是微調模型，建議參考音頻全部在微調訓練集音色內，底模不用管。", "合成语音": "合成語音", "合格的文件夹路径格式举例： E:\\codes\\py39\\vits_vc_gpu\\白鹭霜华测试样例(去文件管理器地址栏拷就行了)。": "合格的資料夾路徑格式舉例： E:\\codes\\py39\\vits_vc_gpu\\白鷺霜華測試範例(去文件管理器地址欄拷就行了)。", "填切割后音频所在目录！读取的音频文件完整路径=该目录-拼接-list文件里波形对应的文件名（不是全路径）。如果留空则使用.list文件里的绝对全路径。": "填切割後音頻所在目錄！讀取的音頻檔案完整路徑=該目錄-拼接-list檔案裡波形對應的檔案名（不是全路徑）。如果留空則使用.list檔案裡的絕對全路徑。", "多语种混合": "多語種混合", "多语种混合(粤语)": "多語種混合 (粵語)", "实际输入的参考文本:": "實際輸入的參考文本:", "实际输入的目标文本(切句后):": "實際輸入的目標文本(切句後):", "实际输入的目标文本(每句):": "實際輸入的目標文本(每句):", "实际输入的目标文本:": "實際輸入的目標文本:", "导出文件格式": "導出檔格式", "开启GPT训练": "開啟GPT訓練", "开启SSL提取": "開啟SSL提取", "开启SoVITS训练": "開啟SoVITS訓練", "开启TTS推理WebUI": "開啟 TTS Inference WebUI", "开启UVR5-WebUI": "開啟 UVR5-WebUI", "开启一键三连": "開啟一鍵三連", "开启打标WebUI": "開啟 Labeling WebUI", "开启文本获取": "開啟文本獲取", "开启无参考文本模式。不填参考文本亦相当于开启。": "開啟無參考文本模式。不填參考文本亦相當於開啟。", "开启离线批量ASR": "開啟離線批量ASR", "开启语义token提取": "開啟語義token提取", "开启语音切割": "開啟語音切割", "开启语音降噪": "開啟語音降噪", "怎么切": "怎麼切", "总训练轮数total_epoch": "總訓練輪數total_epoch", "总训练轮数total_epoch，不建议太高": "總訓練輪數total_epoch，不建議太高", "打标工具WebUI已关闭": "打標工具WebUI已關閉", "打标工具WebUI已开启": "打標工具WebUI已開啟", "打标工具进程输出信息": "打標工具進程輸出資訊", "指定输出主人声文件夹": "指定输出主人声文件夹", "指定输出非主人声文件夹": "指定输出非主人声文件夹", "按中文句号。切": "按中文句號。切", "按标点符号切": "按標點符號切", "按英文句号.切": "按英文句號.切", "数据类型精度": "數據類型精度", "文本模块学习率权重": "文本模塊學習率權重", "文本进程输出信息": "文本進程輸出資訊", "施工中，请静候佳音": "施工中，請靜候佳音", "日文": "日文", "日英混合": "日英混合", "是否仅保存最新的ckpt文件以节省硬盘空间": "是否僅保存最新的ckpt文件以節省硬盤空間", "是否在每次保存时间点将最终小模型保存至weights文件夹": "是否在每次保存時間點將最終小模型保存至weights文件夾", "是否开启dpo训练选项(实验性)": "是否開啟dpo訓練選項(實驗性)", "是否直接对上次合成结果调整语速和音色。防止随机性。": "是否直接對上次合成結果調整語速和音色，以防止隨機性。", "显卡信息": "顯卡資訊", "本软件以MIT协议开源, 作者不对软件具备任何控制力, 使用软件者、传播软件导出的声音者自负全责. <br>如不认可该条款, 则不能使用或引用软件包内任何代码和文件. 详见根目录<b>LICENSE</b>.": "本軟體以MIT協議開源，作者不對軟體具備任何控制力，使用軟體者、傳播軟體導出的聲音者自負全責。<br>如不認可該條款，則不能使用或引用軟體包內任何代碼和文件。詳見根目錄<b>LICENSE</b>。", "模型": "模型", "模型分为三类：": "模型分為三類：", "模型切换": "模型切換", "每张显卡的batch_size": "每張顯卡的batch_size", "版本": "版本", "粤英混合": "粵英混合", "粤语": "粵語", "终止ASR进程": "終止ASR進程", "终止GPT训练": "終止GPT訓練", "终止SSL提取进程": "終止SSL提取進程", "终止SoVITS训练": "終止SoVITS訓練", "终止一键三连": "終止一鍵三連", "终止文本获取进程": "終止文本獲取進程", "终止语义token提取进程": "終止語義token提取進程", "终止语音切割": "終止語音切割", "终止语音降噪进程": "終止語音降噪進程", "缺少Hubert数据集": "缺少Hubert數據集", "缺少语义数据集": "缺少語義數據集", "缺少音素数据集": "缺少音素數據集", "缺少音频数据集": "缺少音頻數據集", "英文": "英文", "语义token提取进程输出信息": "語義token提取進程輸出資訊", "语速": "語速", "语速调整，高为更快": "調整語速，高為更快", "语音切割进程输出信息": "語音切割進程輸出資訊", "语音降噪进程输出信息": "語音降噪進程輸出資訊", "请上传3~10秒内参考音频，超过会报错！": "請上傳3~10秒內參考音頻，超過會報錯！", "请上传参考音频": "請上傳參考音頻", "请填入推理文本": "請填入推理文本", "请填入正确的List路径": "請填寫正確的列表路徑", "请填入正确的音频文件夹路径": "請填寫正確的音頻文件夾路徑", "请输入有效文本": "請輸入有效文本", "路径不能为空": "路徑不應該為空", "路径错误": "路徑錯誤", "转换": "轉換", "输入待处理音频文件夹路径": "輸入待處理音頻資料夾路徑", "输入文件夹路径": "輸入文件夾路徑", "输出logs/实验名目录下应有23456开头的文件和文件夹": "輸出logs/實驗名目錄下應有23456開頭的文件和文件夾", "输出信息": "輸出訊息", "输出文件夹路径": "輸出文件夾路徑", "输出的语音": "輸出的語音", "选择训练完存放在SoVITS_weights和GPT_weights下的模型。默认的一个是底模，体验5秒Zero Shot TTS用。": "選擇訓練完存放在SoVITS_weights和GPT_weights下的模型。默認的一個是底模，體驗5秒Zero Shot TTS用。", "降噪结果输出文件夹": "降噪結果輸出文件夾", "降噪音频文件输入文件夹": "降噪音頻文件輸入文件夾", "需要合成的文本": "需要合成的文本", "需要合成的语种": "需要合成的語種", "韩文": "韓文", "韩英混合": "韓英混合", "音频加载失败": "無法加載音頻", "音频自动切分输入路径，可文件可文件夹": "音頻自動切分輸入路徑，可文件可文件夾", "预训练的GPT模型路径": "預訓練的GPT模型路徑", "预训练的SSL模型路径": "預訓練的SSL模型路徑", "预训练的SoVITS-D模型路径": "預訓練的SoVITS-D模型路徑", "预训练的SoVITS-G模型路径": "預訓練的SoVITS-G模型路徑", "预训练的中文BERT模型路径": "預訓練的中文BERT模型路徑"}