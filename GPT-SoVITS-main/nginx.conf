# 编辑 nginx 配置文件
sudo cat > /etc/nginx/conf.d/voice-test.conf << 'EOF'
   server {
       listen 8080;
       server_name voice-test.zhonganonline.com;

       # GPT-SoVITS 训练页面（WebUI）
       location / {
           proxy_pass http://127.0.0.1:9874/;  # 将根路径请求代理到本地 9874 端口
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto https;  # 如果使用 HTTPS，请将 $scheme 替换为 https

           # WebSocket 支持
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";

           # 增加超时时间
           proxy_connect_timeout 300s;
           proxy_send_timeout 300s;
           proxy_read_timeout 300s;

           # 文件上传大小限制
           client_max_body_size 100M;
       }

       # GPT-SoVITS 推理服务
       location /inference/ {
           proxy_pass http://127.0.0.1:9872/inference/;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto https;  # 如果使用 HTTPS，请将 $scheme 替换为 https

           # WebSocket 支持
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";

           # 增加超时时间
           proxy_connect_timeout 300s;
           proxy_send_timeout 300s;
           proxy_read_timeout 300s;

           # 文件上传大小限制
           client_max_body_size 100M;
       }

       # GPT-SoVITS 标注页面
       location /labeling/ {
           proxy_pass http://127.0.0.1:9871/labeling/;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto https;

           # WebSocket 支持
           proxy_http_version 1.1;
           proxy_set_header Upgrade $http_upgrade;
           proxy_set_header Connection "upgrade";

           # 超时时间和文件大小限制
           proxy_connect_timeout 300s;
           proxy_send_timeout 300s;
           proxy_read_timeout 300s;
           client_max_body_size 100M;

       }

       access_log /var/log/nginx/voice-test_access.log;
       error_log /var/log/nginx/voice-test_error.log debug;
   }
EOF

# 测试并重启 nginx
sudo nginx -t && sudo systemctl restart nginx
