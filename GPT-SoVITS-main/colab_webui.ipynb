{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "include_colab_link": true}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "accelerator": "GPU"}, "cells": [{"cell_type": "markdown", "metadata": {"id": "view-in-github", "colab_type": "text"}, "source": ["<a href=\"https://colab.research.google.com/github/RVC-Boss/GPT-SoVITS/blob/main/colab_webui.ipynb\" target=\"_parent\"><img src=\"https://colab.research.google.com/assets/colab-badge.svg\" alt=\"Open In Colab\"/></a>"]}, {"cell_type": "markdown", "source": ["环境配置 environment"], "metadata": {"id": "_o6a8GS2lWQM"}}, {"cell_type": "code", "metadata": {"id": "e9b7iFV3dm1f"}, "source": ["!pip install -q condacolab\n", "# Setting up condacolab and installing packages\n", "import condacolab\n", "condacolab.install_from_url(\"https://repo.anaconda.com/miniconda/Miniconda3-py39_23.11.0-2-Linux-x86_64.sh\")\n", "%cd -q /content\n", "!git clone https://github.com/RVC-Boss/GPT-SoVITS\n", "!conda install -y -q -c pytorch -c nvidia cudatoolkit\n", "%cd -q /content/GPT-SoVITS\n", "!conda install -y -q -c conda-forge gcc gxx ffmpeg cmake -c pytorch -c nvidia\n", "!/usr/local/bin/pip install -r requirements.txt"], "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# @title Download pretrained models 下载预训练模型\n", "!mkdir -p /content/GPT-SoVITS/GPT_SoVITS/pretrained_models\n", "!mkdir -p /content/GPT-SoVITS/tools/damo_asr/models\n", "!mkdir -p /content/GPT-SoVITS/tools/uvr5\n", "%cd /content/GPT-SoVITS/GPT_SoVITS/pretrained_models\n", "!git clone https://huggingface.co/lj1995/GPT-SoVITS\n", "%cd /content/GPT-SoVITS/tools/damo_asr/models\n", "!git clone https://www.modelscope.cn/damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch.git\n", "!git clone https://www.modelscope.cn/damo/speech_fsmn_vad_zh-cn-16k-common-pytorch.git\n", "!git clone https://www.modelscope.cn/damo/punc_ct-transformer_zh-cn-common-vocab272727-pytorch.git\n", "# @title UVR5 pretrains 安装uvr5模型\n", "%cd /content/GPT-SoVITS/tools/uvr5\n", "%rm -r uvr5_weights\n", "!git clone https://huggingface.co/Delik/uvr5_weights\n", "!git config core.sparseCheckout true\n", "!mv /content/GPT-SoVITS/GPT_SoVITS/pretrained_models/GPT-SoVITS/* /content/GPT-SoVITS/GPT_SoVITS/pretrained_models/"], "metadata": {"id": "0NgxXg5sjv7z"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["# @title launch WebUI 启动WebUI\n", "!/usr/local/bin/pip install ipykernel\n", "!sed -i '10s/False/True/' /content/GPT-SoVITS/config.py\n", "%cd /content/GPT-SoVITS/\n", "!/usr/local/bin/python  webui.py"], "metadata": {"id": "4oRGUzkrk8C7"}, "execution_count": null, "outputs": []}]}