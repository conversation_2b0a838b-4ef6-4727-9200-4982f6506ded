{"cells": [{"cell_type": "code", "execution_count": null, "id": "45857cb2", "metadata": {"_cell_guid": "b1076dfc-b9ad-4769-8c92-a6c4dae69d19", "_uuid": "8f2839f25d086af736a60e9eeb907d3b93b6e0e5", "execution": {"iopub.execute_input": "2024-02-18T14:43:46.735480Z", "iopub.status.busy": "2024-02-18T14:43:46.735183Z", "iopub.status.idle": "2024-02-18T14:48:10.724175Z", "shell.execute_reply": "2024-02-18T14:48:10.723059Z"}, "papermill": {"duration": 263.994935, "end_time": "2024-02-18T14:48:10.726613", "exception": false, "start_time": "2024-02-18T14:43:46.731678", "status": "completed"}, "tags": []}, "outputs": [], "source": ["!git clone https://github.com/RVC-Boss/GPT-SoVITS.git\n", "%cd GPT-SoVITS\n", "!apt-get update && apt-get install -y --no-install-recommends tzdata ffmpeg libsox-dev parallel aria2 git git-lfs && git lfs install\n", "!pip install -r requirements.txt"]}, {"cell_type": "code", "execution_count": null, "id": "b9d346b4", "metadata": {"execution": {"iopub.execute_input": "2024-02-18T14:48:10.815802Z", "iopub.status.busy": "2024-02-18T14:48:10.814899Z", "iopub.status.idle": "2024-02-18T14:50:31.253276Z", "shell.execute_reply": "2024-02-18T14:50:31.252024Z"}, "papermill": {"duration": 140.484893, "end_time": "2024-02-18T14:50:31.255720", "exception": false, "start_time": "2024-02-18T14:48:10.770827", "status": "completed"}, "tags": []}, "outputs": [], "source": ["# @title Download pretrained models 下载预训练模型\n", "!mkdir -p /kaggle/working/GPT-SoVITS/GPT_SoVITS/pretrained_models\n", "!mkdir -p /kaggle/working/GPT-SoVITS/tools/asr/models\n", "!mkdir -p /kaggle/working/GPT-SoVITS/tools/uvr5\n", "%cd /kaggle/working/GPT-SoVITS/GPT_SoVITS/pretrained_models\n", "!git clone https://huggingface.co/lj1995/GPT-SoVITS\n", "%cd /kaggle/working/GPT-SoVITS/tools/asr/models\n", "!git clone https://www.modelscope.cn/damo/speech_paraformer-large_asr_nat-zh-cn-16k-common-vocab8404-pytorch.git\n", "!git clone https://www.modelscope.cn/damo/speech_fsmn_vad_zh-cn-16k-common-pytorch.git\n", "!git clone https://www.modelscope.cn/damo/punc_ct-transformer_zh-cn-common-vocab272727-pytorch.git\n", "# # @title UVR5 pretrains 安装uvr5模型\n", "%cd /kaggle/working/GPT-SoVITS/tools/uvr5\n", "!git clone https://huggingface.co/Delik/uvr5_weights\n", "!git config core.sparseCheckout true\n", "!mv /kaggle/working/GPT-SoVITS/GPT_SoVITS/pretrained_models/GPT-SoVITS/* /kaggle/working/GPT-SoVITS/GPT_SoVITS/pretrained_models/"]}, {"cell_type": "code", "execution_count": null, "id": "ea94d245", "metadata": {"execution": {"iopub.execute_input": "2024-02-18T14:29:01.071549Z", "iopub.status.busy": "2024-02-18T14:29:01.070592Z", "iopub.status.idle": "2024-02-18T14:40:45.318368Z", "shell.execute_reply": "2024-02-18T14:40:45.317130Z", "shell.execute_reply.started": "2024-02-18T14:29:01.071512Z"}, "papermill": {"duration": null, "end_time": null, "exception": false, "start_time": "2024-02-18T14:50:31.309013", "status": "running"}, "tags": []}, "outputs": [], "source": ["# @title launch WebUI 启动WebUI\n", "%cd /kaggle/working/GPT-SoVITS/\n", "!npm install -g localtunnel\n", "import subprocess\n", "import threading\n", "import time\n", "import socket\n", "import urllib.request\n", "def iframe_thread(port):\n", "    while True:\n", "        time.sleep(0.5)\n", "        sock= socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "        result = sock.connect_ex(('127.0.0.1', port))\n", "        if result == 0:\n", "            break\n", "        sock.close()\n", "\n", "        from colorama import Fore, Style\n", "    print (Fore.GREEN + \"\\nIP: \", Fore. RED, urllib.request.urlopen('https://ipv4.icanhazip.com').read().decode('utf8').strip(\"\\n\"), \"\\n\", Style. RESET_ALL)\n", "    p = subprocess.Popen([\"lt\", \"--port\", \"{}\".format(port)], stdout=subprocess.PIPE)\n", "    for line in p.stdout:\n", "        print(line.decode(), end='')\n", "threading.Thread (target=iframe_thread, daemon=True, args=(9874,)).start()\n", "\n", "!python  webui.py"]}, {"cell_type": "code", "execution_count": null, "id": "dda88a6d", "metadata": {"execution": {"iopub.execute_input": "2024-02-18T14:40:56.880608Z", "iopub.status.busy": "2024-02-18T14:40:56.879879Z"}, "papermill": {"duration": null, "end_time": null, "exception": null, "start_time": null, "status": "pending"}, "tags": []}, "outputs": [], "source": ["# 开启推理页面\n", "%cd /kaggle/working/GPT-SoVITS/\n", "!npm install -g localtunnel\n", "import subprocess\n", "import threading\n", "import time\n", "import socket\n", "import urllib.request\n", "def iframe_thread(port):\n", "    while True:\n", "        time.sleep(0.5)\n", "        sock= socket.socket(socket.AF_INET, socket.SOCK_STREAM)\n", "        result = sock.connect_ex(('127.0.0.1', port))\n", "        if result == 0:\n", "            break\n", "        sock.close()\n", "\n", "        from colorama import Fore, Style\n", "    print (Fore.GREEN + \"\\nIP: \", Fore. RED, urllib.request.urlopen('https://ipv4.icanhazip.com').read().decode('utf8').strip(\"\\n\"), \"\\n\", Style. RESET_ALL)\n", "    p = subprocess.Popen([\"lt\", \"--port\", \"{}\".format(port)], stdout=subprocess.PIPE)\n", "    for line in p.stdout:\n", "        print(line.decode(), end='')\n", "threading.Thread (target=iframe_thread, daemon=True, args=(9872,)).start()\n", "\n", "!python  ./GPT_SoVITS/inference_webui.py"]}], "metadata": {"kaggle": {"accelerator": "nvidiaTeslaT4", "dataSources": [{"datasetId": 4459328, "sourceId": 7649639, "sourceType": "datasetVersion"}], "dockerImageVersionId": 30646, "isGpuEnabled": true, "isInternetEnabled": true, "language": "python", "sourceType": "notebook"}, "kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}, "papermill": {"default_parameters": {}, "duration": null, "end_time": null, "environment_variables": {}, "exception": null, "input_path": "__notebook__.ipynb", "output_path": "__notebook__.ipynb", "parameters": {}, "start_time": "2024-02-18T14:43:44.011910", "version": "2.5.0"}}, "nbformat": 4, "nbformat_minor": 5}