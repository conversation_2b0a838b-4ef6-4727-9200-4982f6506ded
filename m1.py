@tool
def detect_faq(text='', preId_list=None, history='', msg='', flow='A', max_length=10):
    text = text.replace('，', ',')
    
    # 判断文本长度是否小于指定长度
    is_short_text = len(history) < max_length
    
    if preId_list is None:
        preId_list = []
    else:
        # 过滤掉 preId_list 中的 "None"
        preId_list = [x for x in preId_list if x != "None"]

    # 定义需要添加 '1' 后缀的特定 FAQ 标签
    specific_ids = ['SZ37']  # 目前只有 SZ37 需要处理

    # 定义需要特殊处理的sendLink ID数组
    sendlink_ids = ["SZ4", "SZ16", "SZ37"]

    # 定义各个 FAQ 的关键词列表
    faq_keywords = {
        'SY': ['正在通话中', '留言', '超时', '机主', '无人接听', "录制", "请按", "助手", "主人",
               '转达', '来电秘书', '爱莫能助', '助理', '呼叫', '喂，你好，呃，你有什么事？', '不方便接电话', '总共需要还多少钱？', '逾期的话利息是多少来着', '请问您还有什么要补充的', '所需还款金额数是多少', '得还多少钱呀？', '请问逾期利息是多少呢？', '其他需要补充', '智能'],
        'SZ16': ['短信', '链接'],
        'SZ10': ['非法', '是高利贷', '乱收费'],
        'SZ28': ['权益费', 'VIP', '99元', '会员'],
        'SZ4': ['代扣'],
        'SZ37': ['对公', '企业账户'],
        'SZ6': ['机器人', '是人工'],
        'G03': ['timeout']
    }

    intent_id = None

    # 兼容处理 \n 和 \\n
    history = history.replace('\\n', '\n')
    msg = msg.replace('\\n', '\n')

    # 提取并分离历史记录和客户当轮回复
    history_lines = history.strip().split("\n")
    msg_lines = msg.strip().split("\n")

    # 提炼历史记录
    extracted_history = "\n".join(history_lines[:-1])  # 去掉最后一行，因为它属于当轮回复

    # 提炼当轮回复
    # 最后一行作为AI的部分，加上客户的当轮回复
    extracted_msg = f"{history_lines[-1]}\n{msg_lines[-1]}"

    # 检查是否之前已经命中过 'SY'
    if 'SY' in preId_list:
        new_preId_list = preId_list + ['SY']
        return {
            "intent_id": 'SY',
            "extracted_history": extracted_history,
            "extracted_msg": extracted_msg,
            "preId_list": new_preId_list,  # 移除了去重逻辑
            "is_short_text": is_short_text
        }

    for faq, keywords in faq_keywords.items():
        pattern = '|'.join(re.escape(keyword)
                           for keyword in keywords)  # 使用 re.escape 确保关键词安全处理
        if re.search(pattern, text, re.IGNORECASE):  # 添加大小写不敏感匹配
            if faq == 'G03':
                if 'G03' not in preId_list:
                    intent_id = 'G03'
                else:
                    if 'G031' in preId_list:
                        intent_id = 'G0311'
                    else:
                        intent_id = 'G031'
            elif faq in specific_ids and faq in preId_list:
                intent_id = f"{faq}1"
            else:
                intent_id = faq

            # 添加 sendLink 处理逻辑
            if faq in sendlink_ids and "sendLink" not in preId_list:
                intent_id = "sendLink"

            break

    # 如果没有匹配到任何 FAQ，返回 'None'
    if intent_id is None:
        intent_id = 'None'
        # 当 intent_id 为 'None' 时，不添加到 preId_list
    else:
        # 如果 intent_id 不是 'None'，添加到 preId_list
        preId_list.append(intent_id)

    valid_flows = ['B1', 'B2', 'C', 'D']
    if flow not in valid_flows:
        return {
            "intent_id": 'None',
            "preId_list": preId_list,
            "extracted_history": extracted_history,
            "extracted_msg": extracted_msg,
            "is_short_text": is_short_text
        }

    return {
        "intent_id": intent_id,
        "preId_list": preId_list,
        "extracted_history": extracted_history,
        "extracted_msg": extracted_msg,
        "is_short_text": is_short_text
    }