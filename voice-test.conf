# 备份原配置文件
cp /etc/nginx/conf.d/voice-test.conf /etc/nginx/conf.d/voice-test.conf.bak

# 创建新的配置文件
cat << 'EOF' > /etc/nginx/conf.d/voice-test.conf
server {
    listen 8080;
    server_name voice-test.zhonganonline.com;

    root /var/www/html/voice-test;
    index index.html;

    # 主页
    location = / {
        try_files /index.html =404;
    }

    # GPTSoVITS 训练服务
    location /gptsovits/config {
        proxy_pass http://localhost:9874;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # GPTSoVITS 推理服务
    location /gptsovits/reasoning {
        proxy_pass http://localhost:9872;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # WebSocket 支持
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }

    # CosyVoice Reasoning
    location /cosyvoice/reasoning {
        try_files $uri $uri/ /cosyvoice/reasoning/index.html;
    }

    access_log /var/log/nginx/voice-test_access.log;
    error_log /var/log/nginx/voice-test_error.log;
}
EOF

# 测试配置
nginx -t

# 如果测试通过，重启 nginx
systemctl restart nginx

https://voice-test.zhonganonline.com

