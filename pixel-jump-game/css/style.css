@import url('https://fonts.googleapis.com/css2?family=Press+Start+2P&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Press Start 2P', monospace;
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
    color: white;
    overflow: hidden;
    user-select: none;
}

.game-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    min-height: 100vh;
    padding: 20px;
}

.game-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 1200px;
    margin-bottom: 10px;
    padding: 10px 20px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 10px;
    border: 2px solid #00ff00;
}

.game-title {
    font-size: 24px;
    color: #00ff00;
    text-shadow: 2px 2px 0px #000;
}

.game-stats {
    display: flex;
    gap: 30px;
}

.stat {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.stat-label {
    font-size: 10px;
    color: #ccc;
}

.stat span:last-child {
    font-size: 16px;
    color: #ffff00;
    text-shadow: 1px 1px 0px #000;
}

#gameCanvas {
    border: 4px solid #00ff00;
    border-radius: 10px;
    background: #87CEEB;
    box-shadow: 0 0 20px rgba(0, 255, 0, 0.5);
    image-rendering: pixelated;
    image-rendering: -moz-crisp-edges;
    image-rendering: crisp-edges;
}

.game-controls {
    margin-top: 10px;
    padding: 15px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 10px;
    border: 2px solid #00ff00;
}

.control-info {
    display: flex;
    gap: 30px;
    justify-content: center;
}

.control-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.key {
    padding: 5px 10px;
    background: #333;
    border: 2px solid #666;
    border-radius: 5px;
    font-size: 10px;
    color: #00ff00;
}

.action {
    font-size: 8px;
    color: #ccc;
}

.game-menu, .game-over, .level-complete, .instructions {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.menu-content, .game-over-content, .level-complete-content, .instructions-content {
    background: linear-gradient(135deg, #2a5298 0%, #1e3c72 100%);
    padding: 40px;
    border-radius: 15px;
    border: 4px solid #00ff00;
    text-align: center;
    box-shadow: 0 0 30px rgba(0, 255, 0, 0.5);
    max-width: 600px;
    width: 90%;
}

.menu-content h2, .game-over-content h2, .level-complete-content h2, .instructions-content h2 {
    font-size: 24px;
    color: #00ff00;
    margin-bottom: 20px;
    text-shadow: 2px 2px 0px #000;
}

.menu-content p, .game-over-content p, .level-complete-content p {
    font-size: 12px;
    margin-bottom: 20px;
    color: #ccc;
}

.menu-btn {
    font-family: 'Press Start 2P', monospace;
    font-size: 12px;
    padding: 15px 30px;
    margin: 10px;
    background: #333;
    color: #00ff00;
    border: 2px solid #00ff00;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.menu-btn:hover {
    background: #00ff00;
    color: #000;
    box-shadow: 0 0 15px rgba(0, 255, 0, 0.7);
}

.hidden {
    display: none !important;
}

.instruction-section {
    text-align: left;
    margin-bottom: 20px;
}

.instruction-section h3 {
    font-size: 14px;
    color: #ffff00;
    margin-bottom: 10px;
}

.instruction-section p, .instruction-section li {
    font-size: 10px;
    line-height: 1.6;
    color: #ccc;
    margin-bottom: 5px;
}

.instruction-section ul {
    list-style: none;
    padding-left: 20px;
}

.instruction-section li:before {
    content: "▶ ";
    color: #00ff00;
}

@media (max-width: 1280px) {
    .game-header {
        width: 100%;
        max-width: 1200px;
    }
    
    #gameCanvas {
        width: 100%;
        max-width: 1200px;
        height: auto;
    }
}

/* 游戏内提示 */
.game-tip {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: #00ff00;
    padding: 20px;
    border-radius: 10px;
    border: 2px solid #00ff00;
    font-size: 12px;
    text-align: center;
    z-index: 100;
    animation: fadeInOut 3s ease-in-out;
}

@keyframes fadeInOut {
    0%, 100% { opacity: 0; }
    50% { opacity: 1; }
}
