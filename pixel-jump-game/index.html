<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>像素跳跃大师 - Pixel Jump Master</title>
    <link rel="stylesheet" href="css/style.css">
</head>
<body>
    <div class="game-container">
        <div class="game-header">
            <div class="game-title">像素跳跃大师</div>
            <div class="game-stats">
                <div class="stat">
                    <span class="stat-label">生命:</span>
                    <span id="lives">3</span>
                </div>
                <div class="stat">
                    <span class="stat-label">分数:</span>
                    <span id="score">0</span>
                </div>
                <div class="stat">
                    <span class="stat-label">关卡:</span>
                    <span id="level">1</span>
                </div>
            </div>
        </div>

        <canvas id="gameCanvas" width="1200" height="600"></canvas>

        <div class="game-controls">
            <div class="control-info">
                <div class="control-item">
                    <span class="key">A/D</span>
                    <span class="action">移动</span>
                </div>
                <div class="control-item">
                    <span class="key">W/空格</span>
                    <span class="action">跳跃</span>
                </div>
                <div class="control-item">
                    <span class="key">R</span>
                    <span class="action">重新开始</span>
                </div>
            </div>
        </div>

        <div id="gameMenu" class="game-menu">
            <div class="menu-content">
                <h2>像素跳跃大师</h2>
                <p>挑战你的跳跃技巧！</p>
                <button id="startBtn" class="menu-btn">开始游戏</button>
                <button id="instructionsBtn" class="menu-btn">游戏说明</button>
            </div>
        </div>

        <div id="gameOver" class="game-over hidden">
            <div class="game-over-content">
                <h2>游戏结束</h2>
                <p>最终分数: <span id="finalScore">0</span></p>
                <p>到达关卡: <span id="finalLevel">1</span></p>
                <button id="restartBtn" class="menu-btn">重新开始</button>
                <button id="menuBtn" class="menu-btn">返回菜单</button>
            </div>
        </div>

        <div id="levelComplete" class="level-complete hidden">
            <div class="level-complete-content">
                <h2>关卡完成！</h2>
                <p>恭喜通过关卡 <span id="completedLevel">1</span></p>
                <p>获得分数: <span id="levelScore">0</span></p>
                <button id="nextLevelBtn" class="menu-btn">下一关</button>
            </div>
        </div>

        <div id="instructions" class="instructions hidden">
            <div class="instructions-content">
                <h2>游戏说明</h2>
                <div class="instruction-section">
                    <h3>目标</h3>
                    <p>控制像素小人跳跃穿越危险的关卡，收集金币，避开敌人和陷阱！</p>
                </div>
                <div class="instruction-section">
                    <h3>控制</h3>
                    <ul>
                        <li><strong>A/D键</strong> - 左右移动</li>
                        <li><strong>W键/空格键</strong> - 跳跃（可二段跳）</li>
                        <li><strong>R键</strong> - 重新开始当前关卡</li>
                    </ul>
                </div>
                <div class="instruction-section">
                    <h3>游戏元素</h3>
                    <ul>
                        <li><strong>金币</strong> - 收集获得分数</li>
                        <li><strong>红色尖刺</strong> - 碰到会失去生命</li>
                        <li><strong>移动平台</strong> - 会移动的平台</li>
                        <li><strong>敌人</strong> - 避开它们！</li>
                    </ul>
                </div>
                <button id="backBtn" class="menu-btn">返回</button>
            </div>
        </div>
    </div>

    <script src="js/audio.js"></script>
    <script src="js/particles.js"></script>
    <script src="js/powerups.js"></script>
    <script src="js/combo.js"></script>
    <script src="js/achievements.js"></script>
    <script src="js/boss.js"></script>
    <script src="js/player.js"></script>
    <script src="js/enemies.js"></script>
    <script src="js/level.js"></script>
    <script src="js/game.js"></script>
</body>
</html>
