// 玩家类
class Player {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.width = 24;
        this.height = 32;
        this.vx = 0;
        this.vy = 0;
        this.speed = 6; // 提高移动速度
        this.acceleration = 0.8; // 加速度
        this.friction = 0.85; // 摩擦力
        this.airFriction = 0.95; // 空中摩擦力
        this.jumpPower = 14; // 提高跳跃力
        this.gravity = 0.6; // 稍微增加重力
        this.maxFallSpeed = 16;
        this.onGround = false;
        this.canDoubleJump = false;
        this.hasDoubleJumped = false;
        this.facing = 1; // 1 = 右, -1 = 左

        // 土狼时间（离开平台后仍可跳跃的时间）
        this.coyoteTime = 0;
        this.maxCoyoteTime = 8;

        // 跳跃缓冲（提前按跳跃键的缓冲时间）
        this.jumpBuffer = 0;
        this.maxJumpBuffer = 8;

        // 可变跳跃高度
        this.jumpHeld = false;
        this.minJumpTime = 0;
        this.maxJumpTime = 15;

        // 动画相关
        this.animFrame = 0;
        this.animTimer = 0;
        this.animSpeed = 6; // 更快的动画
        this.state = 'idle'; // idle, running, jumping, falling

        // 无敌时间
        this.invulnerable = false;
        this.invulnerableTime = 0;
        this.maxInvulnerableTime = 120; // 2秒

        // 视觉效果
        this.squashScale = 1;
        this.landingEffect = false;
        this.wallSliding = false;
        this.wallJumpCooldown = 0;
    }

    update(keys, platforms, enemies, collectibles, spikes, particleSystem, audioManager) {
        this.handleInput(keys, particleSystem, audioManager);
        this.updatePhysics();
        this.checkCollisions(platforms, enemies, collectibles, spikes, particleSystem, audioManager);
        this.updateAnimation();
        this.updateEffects();

        if (this.invulnerable) {
            this.invulnerableTime--;
            if (this.invulnerableTime <= 0) {
                this.invulnerable = false;
            }
        }
    }

    handleInput(keys, particleSystem, audioManager) {
        // 更新跳跃缓冲
        if (keys['w'] || keys['W'] || keys[' ']) {
            this.jumpBuffer = this.maxJumpBuffer;
            this.jumpHeld = true;
        } else {
            this.jumpHeld = false;
            if (this.jumpBuffer > 0) this.jumpBuffer--;
        }

        // 水平移动 - 使用加速度系统
        const targetVx = (keys['a'] || keys['A'] || keys['ArrowLeft']) ? -this.speed :
                        (keys['d'] || keys['D'] || keys['ArrowRight']) ? this.speed : 0;

        if (targetVx !== 0) {
            this.facing = targetVx > 0 ? 1 : -1;
            this.state = this.onGround ? 'running' : this.state;

            // 加速到目标速度
            if (Math.abs(this.vx) < Math.abs(targetVx)) {
                this.vx += targetVx > 0 ? this.acceleration : -this.acceleration;
            } else {
                this.vx = targetVx;
            }
        } else {
            // 应用摩擦力
            const frictionValue = this.onGround ? this.friction : this.airFriction;
            this.vx *= frictionValue;

            if (this.onGround && Math.abs(this.vx) < 0.1) {
                this.vx = 0;
                this.state = 'idle';
            }
        }

        // 跳跃系统 - 土狼时间和跳跃缓冲
        const canJump = (this.onGround || this.coyoteTime > 0) && this.jumpBuffer > 0;
        const canDoubleJump = this.canDoubleJump && !this.hasDoubleJumped && this.jumpBuffer > 0;

        if (canJump && !this.jumpPressed) {
            this.performJump(particleSystem, audioManager, false);
            this.jumpPressed = true;
        } else if (canDoubleJump && !this.jumpPressed) {
            this.performJump(particleSystem, audioManager, true);
            this.jumpPressed = true;
        }

        // 可变跳跃高度
        if (!this.jumpHeld && this.vy < 0 && this.minJumpTime <= 0) {
            this.vy *= 0.5; // 提前结束跳跃
        }

        if (this.minJumpTime > 0) this.minJumpTime--;

        if (!(keys['w'] || keys['W'] || keys[' '])) {
            this.jumpPressed = false;
        }
    }

    performJump(particleSystem, audioManager, isDoubleJump) {
        if (isDoubleJump) {
            this.vy = -this.jumpPower * 0.85;
            this.hasDoubleJumped = true;
            this.canDoubleJump = false;
            particleSystem.createExplosion(this.x + this.width/2, this.y + this.height/2, '#00ffff', 8);
            this.squashScale = 0.7;
            audioManager.playSound('doubleJump');
        } else {
            this.vy = -this.jumpPower;
            this.onGround = false;
            this.canDoubleJump = true;
            this.hasDoubleJumped = false;
            this.coyoteTime = 0;
            particleSystem.createJumpDust(this.x + this.width/2, this.y + this.height);
            this.squashScale = 0.8;
            audioManager.playSound('jump');
        }

        this.state = 'jumping';
        this.minJumpTime = this.maxJumpTime;
        this.jumpBuffer = 0;
    }

    updatePhysics() {
        // 更新土狼时间
        if (this.onGround) {
            this.coyoteTime = this.maxCoyoteTime;
        } else if (this.coyoteTime > 0) {
            this.coyoteTime--;
        }

        // 重力
        this.vy += this.gravity;
        if (this.vy > this.maxFallSpeed) {
            this.vy = this.maxFallSpeed;
        }

        // 更新位置
        this.x += this.vx;
        this.y += this.vy;

        // 更新状态
        if (!this.onGround) {
            this.state = this.vy < 0 ? 'jumping' : 'falling';
        }

        // 更新墙跳冷却
        if (this.wallJumpCooldown > 0) {
            this.wallJumpCooldown--;
        }
    }

    checkCollisions(platforms, enemies, collectibles, spikes, particleSystem, audioManager) {
        this.onGround = false;

        // 平台碰撞
        platforms.forEach(platform => {
            if (this.intersects(platform)) {
                // 从上方落下
                if (this.vy > 0 && this.y < platform.y) {
                    this.y = platform.y - this.height;
                    this.vy = 0;
                    this.onGround = true;
                    this.canDoubleJump = true;
                    this.hasDoubleJumped = false;

                    if (this.landingEffect) {
                        particleSystem.createJumpDust(this.x + this.width/2, this.y + this.height);
                        this.squashScale = 1.2; // 着陆时拉伸效果
                        this.landingEffect = false;
                        audioManager.playSound('land');
                    }
                }
                // 从下方撞击
                else if (this.vy < 0 && this.y > platform.y) {
                    this.y = platform.y + platform.height;
                    this.vy = 0;
                }
                // 从左侧撞击
                else if (this.vx > 0 && this.x < platform.x) {
                    this.x = platform.x - this.width;
                    this.vx = 0;
                }
                // 从右侧撞击
                else if (this.vx < 0 && this.x > platform.x) {
                    this.x = platform.x + platform.width;
                    this.vx = 0;
                }
            }
        });

        // 收集品碰撞
        for (let i = collectibles.length - 1; i >= 0; i--) {
            if (this.intersects(collectibles[i])) {
                particleSystem.createCoinEffect(collectibles[i].x + collectibles[i].width/2,
                                              collectibles[i].y + collectibles[i].height/2);
                collectibles.splice(i, 1);
                return { type: 'coin', points: 100 };
            }
        }

        // 尖刺碰撞
        spikes.forEach(spike => {
            if (this.intersects(spike) && !this.invulnerable) {
                this.takeDamage(particleSystem);
            }
        });

        // 敌人碰撞
        enemies.forEach(enemy => {
            if (this.intersects(enemy) && !this.invulnerable) {
                // 如果玩家从上方跳到敌人身上
                if (this.vy > 0 && this.y < enemy.y - 10) {
                    this.vy = -this.jumpPower * 0.6; // 弹跳
                    enemy.takeDamage();
                    particleSystem.createExplosion(enemy.x + enemy.width/2, enemy.y + enemy.height/2, '#ff6600');
                    return { type: 'enemy', points: 200 };
                } else {
                    this.takeDamage(particleSystem);
                }
            }
        });

        // 设置着陆效果标志
        if (!this.onGround && this.vy > 5) {
            this.landingEffect = true;
        }

        return null;
    }

    takeDamage(particleSystem) {
        if (!this.invulnerable) {
            this.invulnerable = true;
            this.invulnerableTime = this.maxInvulnerableTime;
            particleSystem.createDeathEffect(this.x + this.width/2, this.y + this.height/2);

            // 击退效果
            this.vy = -8;
            this.vx = this.facing * -3;

            return true;
        }
        return false;
    }

    updateAnimation() {
        this.animTimer++;
        if (this.animTimer >= this.animSpeed) {
            this.animTimer = 0;
            this.animFrame = (this.animFrame + 1) % 4;
        }
    }

    updateEffects() {
        // 恢复压缩/拉伸效果
        if (this.squashScale < 1) {
            this.squashScale += 0.05;
            if (this.squashScale > 1) this.squashScale = 1;
        } else if (this.squashScale > 1) {
            this.squashScale -= 0.05;
            if (this.squashScale < 1) this.squashScale = 1;
        }
    }

    intersects(rect) {
        return this.x < rect.x + rect.width &&
               this.x + this.width > rect.x &&
               this.y < rect.y + rect.height &&
               this.y + this.height > rect.y;
    }

    draw(ctx) {
        ctx.save();

        // 无敌闪烁效果
        if (this.invulnerable && Math.floor(this.invulnerableTime / 5) % 2) {
            ctx.globalAlpha = 0.5;
        }

        // 应用压缩/拉伸效果
        const centerX = this.x + this.width / 2;
        const centerY = this.y + this.height / 2;
        ctx.translate(centerX, centerY);
        ctx.scale(this.facing, this.squashScale);
        ctx.translate(-this.width / 2, -this.height / 2);

        // 绘制玩家（像素风格）
        this.drawPixelPlayer(ctx);

        ctx.restore();
    }

    drawPixelPlayer(ctx) {
        const colors = {
            skin: '#ffdbac',
            shirt: '#ff6b6b',
            pants: '#4ecdc4',
            shoes: '#45b7d1',
            hair: '#8b4513'
        };

        // 头部
        ctx.fillStyle = colors.skin;
        ctx.fillRect(6, 0, 12, 10);

        // 头发
        ctx.fillStyle = colors.hair;
        ctx.fillRect(4, 0, 16, 6);

        // 眼睛
        ctx.fillStyle = '#000';
        ctx.fillRect(8, 3, 2, 2);
        ctx.fillRect(14, 3, 2, 2);

        // 身体
        ctx.fillStyle = colors.shirt;
        ctx.fillRect(4, 10, 16, 12);

        // 手臂
        ctx.fillStyle = colors.skin;
        if (this.state === 'running') {
            // 跑步时摆动手臂
            const armOffset = Math.sin(this.animFrame * 0.5) * 2;
            ctx.fillRect(0, 12 + armOffset, 4, 8);
            ctx.fillRect(20, 12 - armOffset, 4, 8);
        } else {
            ctx.fillRect(0, 12, 4, 8);
            ctx.fillRect(20, 12, 4, 8);
        }

        // 腿部
        ctx.fillStyle = colors.pants;
        if (this.state === 'running' && this.onGround) {
            // 跑步时腿部动画
            const legOffset = Math.sin(this.animFrame * 0.8) * 3;
            ctx.fillRect(6, 22, 5, 10);
            ctx.fillRect(13, 22, 5, 10);

            // 脚部
            ctx.fillStyle = colors.shoes;
            ctx.fillRect(4, 30 + legOffset, 8, 4);
            ctx.fillRect(12, 30 - legOffset, 8, 4);
        } else {
            ctx.fillRect(6, 22, 5, 10);
            ctx.fillRect(13, 22, 5, 10);

            // 脚部
            ctx.fillStyle = colors.shoes;
            ctx.fillRect(4, 30, 8, 4);
            ctx.fillRect(12, 30, 8, 4);
        }
    }

    reset(x, y) {
        this.x = x;
        this.y = y;
        this.vx = 0;
        this.vy = 0;
        this.onGround = false;
        this.canDoubleJump = false;
        this.hasDoubleJumped = false;
        this.invulnerable = false;
        this.invulnerableTime = 0;
        this.state = 'idle';
        this.squashScale = 1;
        this.landingEffect = false;
    }
}
