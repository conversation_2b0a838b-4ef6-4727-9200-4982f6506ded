// 粒子系统
class ParticleSystem {
    constructor() {
        this.particles = [];
    }

    createExplosion(x, y, color = '#ffff00', count = 8) {
        for (let i = 0; i < count; i++) {
            this.particles.push(new Particle(x, y, color, 'explosion'));
        }
    }

    createCoinEffect(x, y) {
        for (let i = 0; i < 6; i++) {
            this.particles.push(new Particle(x, y, '#ffff00', 'coin'));
        }
    }

    createJumpDust(x, y) {
        for (let i = 0; i < 4; i++) {
            this.particles.push(new Particle(x, y, '#8B4513', 'dust'));
        }
    }

    createDeathEffect(x, y) {
        for (let i = 0; i < 12; i++) {
            this.particles.push(new Particle(x, y, '#ff0000', 'death'));
        }
    }

    update() {
        for (let i = this.particles.length - 1; i >= 0; i--) {
            this.particles[i].update();
            if (this.particles[i].life <= 0) {
                this.particles.splice(i, 1);
            }
        }
    }

    draw(ctx) {
        this.particles.forEach(particle => particle.draw(ctx));
    }
}

class Particle {
    constructor(x, y, color, type) {
        this.x = x;
        this.y = y;
        this.color = color;
        this.type = type;
        this.life = 1.0;
        this.maxLife = 1.0;
        
        // 根据类型设置不同的属性
        switch (type) {
            case 'explosion':
                this.vx = (Math.random() - 0.5) * 8;
                this.vy = (Math.random() - 0.5) * 8;
                this.decay = 0.02;
                this.size = Math.random() * 4 + 2;
                break;
            case 'coin':
                this.vx = (Math.random() - 0.5) * 4;
                this.vy = Math.random() * -3 - 2;
                this.decay = 0.015;
                this.size = Math.random() * 3 + 1;
                this.gravity = 0.1;
                break;
            case 'dust':
                this.vx = (Math.random() - 0.5) * 3;
                this.vy = Math.random() * -2 - 1;
                this.decay = 0.03;
                this.size = Math.random() * 2 + 1;
                this.gravity = 0.05;
                break;
            case 'death':
                this.vx = (Math.random() - 0.5) * 10;
                this.vy = Math.random() * -8 - 2;
                this.decay = 0.01;
                this.size = Math.random() * 5 + 3;
                this.gravity = 0.2;
                break;
        }
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        
        if (this.gravity) {
            this.vy += this.gravity;
        }
        
        this.life -= this.decay;
        
        // 添加一些随机性
        if (this.type === 'explosion') {
            this.vx *= 0.98;
            this.vy *= 0.98;
        }
    }

    draw(ctx) {
        ctx.save();
        ctx.globalAlpha = this.life;
        
        switch (this.type) {
            case 'explosion':
                ctx.fillStyle = this.color;
                ctx.fillRect(this.x - this.size/2, this.y - this.size/2, this.size, this.size);
                break;
            case 'coin':
                ctx.fillStyle = this.color;
                ctx.beginPath();
                ctx.arc(this.x, this.y, this.size, 0, Math.PI * 2);
                ctx.fill();
                break;
            case 'dust':
                ctx.fillStyle = this.color;
                ctx.fillRect(this.x - this.size/2, this.y - this.size/2, this.size, this.size);
                break;
            case 'death':
                ctx.fillStyle = this.color;
                ctx.fillRect(this.x - this.size/2, this.y - this.size/2, this.size, this.size);
                // 添加一个内部的亮点
                ctx.fillStyle = '#ffaaaa';
                ctx.fillRect(this.x - this.size/4, this.y - this.size/4, this.size/2, this.size/2);
                break;
        }
        
        ctx.restore();
    }
}

// 背景粒子效果
class BackgroundParticles {
    constructor(canvas) {
        this.canvas = canvas;
        this.particles = [];
        this.init();
    }

    init() {
        for (let i = 0; i < 20; i++) {
            this.particles.push({
                x: Math.random() * this.canvas.width,
                y: Math.random() * this.canvas.height,
                size: Math.random() * 2 + 1,
                speedX: (Math.random() - 0.5) * 0.5,
                speedY: (Math.random() - 0.5) * 0.5,
                opacity: Math.random() * 0.5 + 0.2
            });
        }
    }

    update() {
        this.particles.forEach(particle => {
            particle.x += particle.speedX;
            particle.y += particle.speedY;

            if (particle.x < 0) particle.x = this.canvas.width;
            if (particle.x > this.canvas.width) particle.x = 0;
            if (particle.y < 0) particle.y = this.canvas.height;
            if (particle.y > this.canvas.height) particle.y = 0;
        });
    }

    draw(ctx) {
        ctx.save();
        this.particles.forEach(particle => {
            ctx.globalAlpha = particle.opacity;
            ctx.fillStyle = '#ffffff';
            ctx.fillRect(particle.x, particle.y, particle.size, particle.size);
        });
        ctx.restore();
    }
}
