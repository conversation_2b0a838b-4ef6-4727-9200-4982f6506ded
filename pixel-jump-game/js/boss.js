// Boss系统
class Boss {
    constructor(x, y, type, health) {
        this.x = x;
        this.y = y;
        this.type = type;
        this.health = health;
        this.maxHealth = health;
        this.width = 120;
        this.height = 80;
        this.vx = 0;
        this.vy = 0;
        this.speed = 2;
        this.direction = 1;
        
        // 攻击系统
        this.attackTimer = 0;
        this.attackCooldown = 180; // 3秒
        this.projectiles = [];
        this.isAttacking = false;
        this.attackPhase = 0;
        
        // 动画
        this.animFrame = 0;
        this.animTimer = 0;
        this.flashTimer = 0;
        
        // 状态
        this.phase = 1; // Boss阶段
        this.invulnerable = false;
        this.invulnerableTime = 0;
        this.dead = false;
        this.deathTimer = 0;
        
        // 特殊攻击
        this.specialAttackTimer = 0;
        this.specialAttackCooldown = 600; // 10秒
        this.isCharging = false;
        this.chargeTimer = 0;
        
        this.initBossType();
    }

    initBossType() {
        switch (this.type) {
            case 'dragon':
                this.width = 150;
                this.height = 100;
                this.attackCooldown = 120;
                this.specialAttackCooldown = 480;
                break;
        }
    }

    update(player, platforms, particleSystem, audioManager) {
        if (this.dead) {
            this.deathTimer++;
            if (this.deathTimer > 180) { // 3秒死亡动画
                return true; // 标记为可移除
            }
            return false;
        }

        this.updateMovement(player, platforms);
        this.updateAttacks(player, particleSystem, audioManager);
        this.updateProjectiles(platforms, particleSystem);
        this.updateAnimation();
        this.updateEffects();
        
        return false;
    }

    updateMovement(player, platforms) {
        // Boss移动AI
        switch (this.type) {
            case 'dragon':
                this.updateDragonMovement(player);
                break;
        }
        
        // 应用移动
        this.x += this.vx;
        this.y += this.vy;
        
        // 边界检查
        if (this.x < 0) {
            this.x = 0;
            this.direction = 1;
        }
        if (this.x + this.width > 1200) {
            this.x = 1200 - this.width;
            this.direction = -1;
        }
    }

    updateDragonMovement(player) {
        // 巨龙移动模式
        if (this.isCharging) {
            // 冲锋攻击
            this.vx = this.direction * 8;
            this.vy = 0;
            this.chargeTimer--;
            if (this.chargeTimer <= 0) {
                this.isCharging = false;
                this.vx = 0;
            }
        } else {
            // 正常巡逻
            const distanceToPlayer = Math.abs(player.x - this.x);
            if (distanceToPlayer > 200) {
                // 靠近玩家
                this.vx = player.x > this.x ? 1 : -1;
                this.direction = this.vx > 0 ? 1 : -1;
            } else {
                // 保持距离
                this.vx *= 0.9;
            }
            
            // 垂直移动（飞行）
            const targetY = 150 + Math.sin(Date.now() * 0.002) * 50;
            this.vy = (targetY - this.y) * 0.02;
        }
    }

    updateAttacks(player, particleSystem, audioManager) {
        this.attackTimer++;
        this.specialAttackTimer++;
        
        // 普通攻击
        if (this.attackTimer >= this.attackCooldown) {
            this.performAttack(player, particleSystem, audioManager);
            this.attackTimer = 0;
        }
        
        // 特殊攻击
        if (this.specialAttackTimer >= this.specialAttackCooldown) {
            this.performSpecialAttack(player, particleSystem, audioManager);
            this.specialAttackTimer = 0;
        }
        
        // 根据血量改变攻击模式
        const healthRatio = this.health / this.maxHealth;
        if (healthRatio < 0.5 && this.phase === 1) {
            this.phase = 2;
            this.attackCooldown *= 0.7; // 攻击更频繁
            this.specialAttackCooldown *= 0.8;
        }
        if (healthRatio < 0.25 && this.phase === 2) {
            this.phase = 3;
            this.attackCooldown *= 0.6; // 更加疯狂
            this.specialAttackCooldown *= 0.7;
        }
    }

    performAttack(player, particleSystem, audioManager) {
        switch (this.type) {
            case 'dragon':
                this.dragonFireBreath(player, particleSystem, audioManager);
                break;
        }
    }

    performSpecialAttack(player, particleSystem, audioManager) {
        switch (this.type) {
            case 'dragon':
                if (Math.random() < 0.5) {
                    this.dragonCharge(player, particleSystem, audioManager);
                } else {
                    this.dragonMeteor(player, particleSystem, audioManager);
                }
                break;
        }
    }

    dragonFireBreath(player, particleSystem, audioManager) {
        // 火焰喷射攻击
        const fireballCount = this.phase === 3 ? 5 : this.phase === 2 ? 3 : 1;
        
        for (let i = 0; i < fireballCount; i++) {
            const angle = Math.atan2(player.y - this.y, player.x - this.x);
            const spread = (i - Math.floor(fireballCount / 2)) * 0.3;
            
            this.projectiles.push(new BossProjectile(
                this.x + this.width / 2,
                this.y + this.height / 2,
                Math.cos(angle + spread) * 6,
                Math.sin(angle + spread) * 6,
                'fireball'
            ));
        }
        
        // 火焰粒子效果
        particleSystem.createExplosion(
            this.x + this.width / 2,
            this.y + this.height / 2,
            '#FF4500',
            15
        );
        
        audioManager.playSound('enemyHit'); // 临时使用现有音效
    }

    dragonCharge(player, particleSystem, audioManager) {
        // 冲锋攻击
        this.isCharging = true;
        this.chargeTimer = 60;
        this.direction = player.x > this.x ? 1 : -1;
        
        // 警告效果
        particleSystem.createExplosion(
            this.x + this.width / 2,
            this.y + this.height / 2,
            '#FFFF00',
            20
        );
    }

    dragonMeteor(player, particleSystem, audioManager) {
        // 陨石攻击
        for (let i = 0; i < 3; i++) {
            const targetX = player.x + (Math.random() - 0.5) * 200;
            this.projectiles.push(new BossProjectile(
                targetX,
                -50,
                0,
                8,
                'meteor'
            ));
        }
    }

    updateProjectiles(platforms, particleSystem) {
        for (let i = this.projectiles.length - 1; i >= 0; i--) {
            const projectile = this.projectiles[i];
            projectile.update();
            
            // 检查边界
            if (projectile.x < -50 || projectile.x > 1250 || projectile.y > 650) {
                if (projectile.type === 'meteor') {
                    // 陨石爆炸效果
                    particleSystem.createExplosion(projectile.x, projectile.y, '#FF4500', 12);
                }
                this.projectiles.splice(i, 1);
                continue;
            }
            
            // 检查平台碰撞
            platforms.forEach(platform => {
                if (projectile.intersects(platform)) {
                    particleSystem.createExplosion(projectile.x, projectile.y, '#FF6600', 8);
                    this.projectiles.splice(i, 1);
                }
            });
        }
    }

    updateAnimation() {
        this.animTimer++;
        if (this.animTimer >= 15) {
            this.animTimer = 0;
            this.animFrame = (this.animFrame + 1) % 4;
        }
    }

    updateEffects() {
        if (this.flashTimer > 0) {
            this.flashTimer--;
        }
        
        if (this.invulnerable) {
            this.invulnerableTime--;
            if (this.invulnerableTime <= 0) {
                this.invulnerable = false;
            }
        }
    }

    takeDamage(damage, particleSystem, audioManager) {
        if (this.invulnerable || this.dead) return false;
        
        this.health -= damage;
        this.flashTimer = 10;
        this.invulnerable = true;
        this.invulnerableTime = 30;
        
        // 受伤效果
        particleSystem.createExplosion(
            this.x + this.width / 2,
            this.y + this.height / 2,
            '#FF0000',
            10
        );
        
        audioManager.playSound('enemyHit');
        
        if (this.health <= 0) {
            this.dead = true;
            this.deathTimer = 0;
            // 死亡爆炸
            for (let i = 0; i < 30; i++) {
                setTimeout(() => {
                    particleSystem.createExplosion(
                        this.x + Math.random() * this.width,
                        this.y + Math.random() * this.height,
                        '#FFD700',
                        8
                    );
                }, i * 50);
            }
        }
        
        return true;
    }

    intersects(rect) {
        return this.x < rect.x + rect.width &&
               this.x + this.width > rect.x &&
               this.y < rect.y + rect.height &&
               this.y + this.height > rect.y;
    }

    draw(ctx) {
        ctx.save();
        
        // 受伤闪烁
        if (this.flashTimer > 0 && Math.floor(this.flashTimer / 2) % 2) {
            ctx.globalAlpha = 0.5;
        }
        
        // 死亡效果
        if (this.dead) {
            ctx.globalAlpha = 1 - (this.deathTimer / 180);
            ctx.translate(this.x + this.width/2, this.y + this.height/2);
            ctx.rotate(this.deathTimer * 0.1);
            ctx.translate(-this.width/2, -this.height/2);
        }
        
        switch (this.type) {
            case 'dragon':
                this.drawDragon(ctx);
                break;
        }
        
        // 绘制投射物
        this.projectiles.forEach(projectile => projectile.draw(ctx));
        
        ctx.restore();
        
        // 绘制血条
        this.drawHealthBar(ctx);
    }

    drawDragon(ctx) {
        // 巨龙身体
        ctx.fillStyle = this.phase === 3 ? '#8B0000' : this.phase === 2 ? '#B22222' : '#DC143C';
        ctx.fillRect(this.x, this.y + 20, this.width, this.height - 40);
        
        // 巨龙头部
        ctx.fillStyle = this.phase === 3 ? '#A0522D' : '#CD853F';
        ctx.fillRect(this.x + (this.direction > 0 ? this.width - 40 : 0), this.y, 40, 60);
        
        // 眼睛
        ctx.fillStyle = this.phase === 3 ? '#FF0000' : '#FFFF00';
        const eyeX = this.x + (this.direction > 0 ? this.width - 30 : 10);
        ctx.fillRect(eyeX, this.y + 15, 6, 6);
        ctx.fillRect(eyeX, this.y + 35, 6, 6);
        
        // 翅膀动画
        const wingFlap = Math.sin(this.animFrame * 0.5) * 10;
        ctx.fillStyle = '#8B4513';
        ctx.fillRect(this.x + 20, this.y - 10 + wingFlap, 30, 20);
        ctx.fillRect(this.x + this.width - 50, this.y - 10 - wingFlap, 30, 20);
        
        // 冲锋警告效果
        if (this.isCharging) {
            ctx.fillStyle = `rgba(255, 255, 0, ${Math.sin(Date.now() * 0.02) * 0.3 + 0.7})`;
            ctx.fillRect(this.x - 10, this.y - 10, this.width + 20, this.height + 20);
        }
    }

    drawHealthBar(ctx) {
        const barWidth = 200;
        const barHeight = 20;
        const barX = 500;
        const barY = 50;
        
        // 背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(barX - 5, barY - 5, barWidth + 10, barHeight + 10);
        
        // 血条背景
        ctx.fillStyle = '#333';
        ctx.fillRect(barX, barY, barWidth, barHeight);
        
        // 血条
        const healthRatio = this.health / this.maxHealth;
        const healthColor = healthRatio > 0.6 ? '#00FF00' : healthRatio > 0.3 ? '#FFFF00' : '#FF0000';
        ctx.fillStyle = healthColor;
        ctx.fillRect(barX, barY, barWidth * healthRatio, barHeight);
        
        // Boss名称
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '16px "Press Start 2P"';
        ctx.textAlign = 'center';
        ctx.fillText('机械巨龙', barX + barWidth / 2, barY - 10);
        
        // 血量数字
        ctx.font = '12px "Press Start 2P"';
        ctx.fillText(`${this.health}/${this.maxHealth}`, barX + barWidth / 2, barY + 35);
    }
}

// Boss投射物类
class BossProjectile {
    constructor(x, y, vx, vy, type) {
        this.x = x;
        this.y = y;
        this.vx = vx;
        this.vy = vy;
        this.type = type;
        this.width = 16;
        this.height = 16;
        this.animFrame = 0;
        this.animTimer = 0;
        
        if (type === 'meteor') {
            this.width = 24;
            this.height = 24;
        }
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        
        if (this.type === 'meteor') {
            this.vy += 0.3; // 重力
        }
        
        this.animTimer++;
        if (this.animTimer >= 5) {
            this.animTimer = 0;
            this.animFrame = (this.animFrame + 1) % 4;
        }
    }

    intersects(rect) {
        return this.x < rect.x + rect.width &&
               this.x + this.width > rect.x &&
               this.y < rect.y + rect.height &&
               this.y + this.height > rect.y;
    }

    draw(ctx) {
        ctx.save();
        ctx.translate(this.x + this.width/2, this.y + this.height/2);
        ctx.rotate(this.animFrame * 0.2);
        
        switch (this.type) {
            case 'fireball':
                ctx.fillStyle = '#FF4500';
                ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
                ctx.fillStyle = '#FFD700';
                ctx.fillRect(-this.width/2 + 2, -this.height/2 + 2, this.width - 4, this.height - 4);
                break;
            case 'meteor':
                ctx.fillStyle = '#8B4513';
                ctx.fillRect(-this.width/2, -this.height/2, this.width, this.height);
                ctx.fillStyle = '#FF4500';
                ctx.fillRect(-this.width/2 + 4, -this.height/2 + 4, this.width - 8, this.height - 8);
                break;
        }
        
        ctx.restore();
    }
}
