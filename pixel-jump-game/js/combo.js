// 连击系统
class ComboSystem {
    constructor() {
        this.combo = 0;
        this.maxCombo = 0;
        this.comboTimer = 0;
        this.comboTimeout = 300; // 5秒
        this.multiplier = 1;
        this.comboThresholds = [5, 10, 20, 30, 50];
        this.specialMoves = new Map();
        this.energy = 0;
        this.maxEnergy = 100;
        this.lastActionTime = 0;
    }

    addCombo(action, points = 1) {
        this.combo += points;
        this.comboTimer = this.comboTimeout;
        this.lastActionTime = Date.now();
        
        if (this.combo > this.maxCombo) {
            this.maxCombo = this.combo;
        }
        
        // 更新倍数
        this.updateMultiplier();
        
        // 增加能量
        this.addEnergy(points * 2);
        
        return this.getComboBonus(action);
    }

    updateMultiplier() {
        if (this.combo >= 50) this.multiplier = 5;
        else if (this.combo >= 30) this.multiplier = 4;
        else if (this.combo >= 20) this.multiplier = 3;
        else if (this.combo >= 10) this.multiplier = 2.5;
        else if (this.combo >= 5) this.multiplier = 2;
        else this.multiplier = 1;
    }

    getComboBonus(action) {
        let bonus = 0;
        switch (action) {
            case 'coin':
                bonus = Math.floor(100 * this.multiplier);
                break;
            case 'enemy':
                bonus = Math.floor(200 * this.multiplier);
                break;
            case 'perfect_jump':
                bonus = Math.floor(50 * this.multiplier);
                break;
        }
        return bonus;
    }

    addEnergy(amount) {
        this.energy = Math.min(this.maxEnergy, this.energy + amount);
    }

    useEnergy(amount) {
        if (this.energy >= amount) {
            this.energy -= amount;
            return true;
        }
        return false;
    }

    update() {
        if (this.comboTimer > 0) {
            this.comboTimer--;
        } else if (this.combo > 0) {
            this.resetCombo();
        }
        
        // 自然恢复能量
        if (this.energy < this.maxEnergy) {
            this.energy += 0.1;
        }
    }

    resetCombo() {
        this.combo = 0;
        this.multiplier = 1;
    }

    canUseSpecialMove(moveType) {
        const energyCost = this.getSpecialMoveCost(moveType);
        return this.energy >= energyCost;
    }

    getSpecialMoveCost(moveType) {
        switch (moveType) {
            case 'dash': return 20;
            case 'ground_pound': return 30;
            case 'time_slow': return 50;
            case 'shield_burst': return 40;
            default: return 25;
        }
    }

    useSpecialMove(moveType) {
        const cost = this.getSpecialMoveCost(moveType);
        if (this.useEnergy(cost)) {
            return this.executeSpecialMove(moveType);
        }
        return null;
    }

    executeSpecialMove(moveType) {
        switch (moveType) {
            case 'dash':
                return { type: 'dash', duration: 30, power: 2 };
            case 'ground_pound':
                return { type: 'ground_pound', damage: 3, range: 100 };
            case 'time_slow':
                return { type: 'time_slow', duration: 180, factor: 0.3 };
            case 'shield_burst':
                return { type: 'shield_burst', duration: 120, reflect: true };
        }
        return null;
    }

    getComboRank() {
        if (this.combo >= 50) return 'LEGENDARY';
        if (this.combo >= 30) return 'EPIC';
        if (this.combo >= 20) return 'RARE';
        if (this.combo >= 10) return 'GOOD';
        if (this.combo >= 5) return 'NICE';
        return '';
    }

    getComboColor() {
        if (this.combo >= 50) return '#FFD700';
        if (this.combo >= 30) return '#FF69B4';
        if (this.combo >= 20) return '#9370DB';
        if (this.combo >= 10) return '#00CED1';
        if (this.combo >= 5) return '#32CD32';
        return '#FFFFFF';
    }

    drawComboUI(ctx, canvas) {
        if (this.combo === 0) return;
        
        const x = canvas.width - 250;
        const y = 100;
        
        // 连击背景
        ctx.save();
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.fillRect(x - 10, y - 10, 240, 120);
        
        // 连击数字
        ctx.fillStyle = this.getComboColor();
        ctx.font = '32px "Press Start 2P"';
        ctx.textAlign = 'center';
        ctx.fillText(`${this.combo}`, x + 120, y + 30);
        
        // 连击等级
        const rank = this.getComboRank();
        if (rank) {
            ctx.font = '12px "Press Start 2P"';
            ctx.fillText(rank, x + 120, y + 50);
        }
        
        // 倍数显示
        ctx.fillStyle = '#FFFF00';
        ctx.font = '14px "Press Start 2P"';
        ctx.fillText(`x${this.multiplier}`, x + 120, y + 70);
        
        // 连击计时器
        const timerRatio = this.comboTimer / this.comboTimeout;
        ctx.fillStyle = timerRatio > 0.5 ? '#00FF00' : timerRatio > 0.25 ? '#FFFF00' : '#FF0000';
        ctx.fillRect(x, y + 80, 240 * timerRatio, 8);
        
        ctx.restore();
    }

    drawEnergyUI(ctx, canvas) {
        const x = canvas.width - 250;
        const y = 240;
        const barWidth = 200;
        const barHeight = 20;
        
        ctx.save();
        
        // 能量条背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.fillRect(x - 10, y - 10, barWidth + 20, barHeight + 20);
        
        // 能量条
        ctx.fillStyle = '#333';
        ctx.fillRect(x, y, barWidth, barHeight);
        
        const energyRatio = this.energy / this.maxEnergy;
        ctx.fillStyle = energyRatio > 0.6 ? '#00BFFF' : energyRatio > 0.3 ? '#FFD700' : '#FF4500';
        ctx.fillRect(x, y, barWidth * energyRatio, barHeight);
        
        // 能量文字
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '10px "Press Start 2P"';
        ctx.textAlign = 'center';
        ctx.fillText('ENERGY', x + barWidth / 2, y - 15);
        ctx.fillText(`${Math.floor(this.energy)}/${this.maxEnergy}`, x + barWidth / 2, y + 35);
        
        ctx.restore();
    }

    drawSpecialMoveHints(ctx, canvas) {
        const x = canvas.width - 250;
        const y = 300;
        
        ctx.save();
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(x - 10, y - 10, 240, 100);
        
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '8px "Press Start 2P"';
        ctx.textAlign = 'left';
        
        const moves = [
            { key: 'Q', name: 'DASH', cost: 20 },
            { key: 'E', name: 'GROUND POUND', cost: 30 },
            { key: 'F', name: 'TIME SLOW', cost: 50 },
            { key: 'C', name: 'SHIELD BURST', cost: 40 }
        ];
        
        moves.forEach((move, index) => {
            const canUse = this.energy >= move.cost;
            ctx.fillStyle = canUse ? '#00FF00' : '#666666';
            ctx.fillText(`${move.key}: ${move.name} (${move.cost})`, x, y + index * 20);
        });
        
        ctx.restore();
    }
}

// 特殊技能效果管理器
class SpecialEffectsManager {
    constructor() {
        this.activeEffects = new Map();
        this.timeSlowFactor = 1;
        this.screenShake = { x: 0, y: 0, intensity: 0, duration: 0 };
    }

    addEffect(type, data) {
        this.activeEffects.set(type, {
            ...data,
            startTime: Date.now()
        });
        
        switch (type) {
            case 'time_slow':
                this.timeSlowFactor = data.factor;
                break;
            case 'screen_shake':
                this.screenShake = {
                    intensity: data.intensity || 5,
                    duration: data.duration || 30,
                    x: 0,
                    y: 0
                };
                break;
        }
    }

    update() {
        for (let [type, effect] of this.activeEffects) {
            effect.duration--;
            
            if (effect.duration <= 0) {
                this.removeEffect(type);
            } else {
                this.updateEffect(type, effect);
            }
        }
        
        // 更新屏幕震动
        if (this.screenShake.duration > 0) {
            this.screenShake.duration--;
            this.screenShake.x = (Math.random() - 0.5) * this.screenShake.intensity;
            this.screenShake.y = (Math.random() - 0.5) * this.screenShake.intensity;
            
            if (this.screenShake.duration <= 0) {
                this.screenShake.x = 0;
                this.screenShake.y = 0;
            }
        }
    }

    updateEffect(type, effect) {
        switch (type) {
            case 'time_slow':
                // 时间减慢效果逐渐恢复
                const timeRatio = effect.duration / (effect.originalDuration || effect.duration);
                this.timeSlowFactor = 1 - (1 - effect.factor) * timeRatio;
                break;
        }
    }

    removeEffect(type) {
        this.activeEffects.delete(type);
        
        switch (type) {
            case 'time_slow':
                this.timeSlowFactor = 1;
                break;
        }
    }

    hasEffect(type) {
        return this.activeEffects.has(type);
    }

    getEffect(type) {
        return this.activeEffects.get(type);
    }

    applyTimeScale(deltaTime) {
        return deltaTime * this.timeSlowFactor;
    }

    applyScreenShake(ctx) {
        if (this.screenShake.duration > 0) {
            ctx.translate(this.screenShake.x, this.screenShake.y);
        }
    }

    drawTimeSlowEffect(ctx, canvas) {
        if (this.hasEffect('time_slow')) {
            ctx.save();
            ctx.fillStyle = `rgba(0, 100, 255, 0.1)`;
            ctx.fillRect(0, 0, canvas.width, canvas.height);
            
            // 时间减慢边框效果
            ctx.strokeStyle = '#00BFFF';
            ctx.lineWidth = 4;
            ctx.strokeRect(2, 2, canvas.width - 4, canvas.height - 4);
            
            ctx.restore();
        }
    }

    drawShieldBurstEffect(ctx, player) {
        if (this.hasEffect('shield_burst')) {
            const effect = this.getEffect('shield_burst');
            const radius = 50 + Math.sin(Date.now() * 0.01) * 10;
            
            ctx.save();
            ctx.globalAlpha = 0.6;
            ctx.strokeStyle = '#00FFFF';
            ctx.lineWidth = 3;
            ctx.beginPath();
            ctx.arc(player.x + player.width/2, player.y + player.height/2, radius, 0, Math.PI * 2);
            ctx.stroke();
            
            ctx.globalAlpha = 0.3;
            ctx.fillStyle = '#00FFFF';
            ctx.fill();
            ctx.restore();
        }
    }
}
