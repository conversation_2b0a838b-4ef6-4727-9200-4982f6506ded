// 成就系统
class AchievementSystem {
    constructor() {
        this.achievements = this.createAchievements();
        this.unlockedAchievements = new Set();
        this.notifications = [];
        this.stats = {
            totalJumps: 0,
            totalCoins: 0,
            totalEnemiesKilled: 0,
            totalDeaths: 0,
            maxCombo: 0,
            totalPlayTime: 0,
            levelsCompleted: 0,
            perfectJumps: 0,
            doubleJumps: 0,
            powerUpsCollected: 0,
            bossesDefeated: 0
        };
        
        this.loadProgress();
    }

    createAchievements() {
        return {
            'first_jump': {
                name: '初次跳跃',
                description: '进行第一次跳跃',
                icon: '🦘',
                rarity: 'common',
                condition: (stats) => stats.totalJumps >= 1
            },
            'coin_collector': {
                name: '金币收集者',
                description: '收集100枚金币',
                icon: '💰',
                rarity: 'common',
                condition: (stats) => stats.totalCoins >= 100
            },
            'enemy_slayer': {
                name: '敌人杀手',
                description: '击败50个敌人',
                icon: '⚔️',
                rarity: 'uncommon',
                condition: (stats) => stats.totalEnemiesKilled >= 50
            },
            'combo_master': {
                name: '连击大师',
                description: '达到30连击',
                icon: '🔥',
                rarity: 'rare',
                condition: (stats) => stats.maxCombo >= 30
            },
            'survivor': {
                name: '生存专家',
                description: '不死亡完成一个关卡',
                icon: '🛡️',
                rarity: 'uncommon',
                condition: (stats) => stats.perfectLevels >= 1
            },
            'speed_demon': {
                name: '速度恶魔',
                description: '在60秒内完成一个关卡',
                icon: '⚡',
                rarity: 'rare',
                condition: (stats) => stats.fastestLevel <= 60
            },
            'double_jump_master': {
                name: '二段跳大师',
                description: '使用二段跳100次',
                icon: '🌟',
                rarity: 'uncommon',
                condition: (stats) => stats.doubleJumps >= 100
            },
            'power_hungry': {
                name: '力量渴求者',
                description: '收集25个道具',
                icon: '💎',
                rarity: 'uncommon',
                condition: (stats) => stats.powerUpsCollected >= 25
            },
            'boss_slayer': {
                name: 'Boss杀手',
                description: '击败第一个Boss',
                icon: '🐉',
                rarity: 'epic',
                condition: (stats) => stats.bossesDefeated >= 1
            },
            'perfectionist': {
                name: '完美主义者',
                description: '收集一个关卡中的所有金币',
                icon: '✨',
                rarity: 'rare',
                condition: (stats) => stats.perfectCollections >= 1
            },
            'marathon_runner': {
                name: '马拉松跑者',
                description: '游戏时间超过30分钟',
                icon: '🏃',
                rarity: 'uncommon',
                condition: (stats) => stats.totalPlayTime >= 1800 // 30分钟
            },
            'death_defier': {
                name: '死神蔑视者',
                description: '死亡次数少于5次完成游戏',
                icon: '💀',
                rarity: 'epic',
                condition: (stats) => stats.totalDeaths < 5 && stats.levelsCompleted >= 5
            },
            'combo_legend': {
                name: '连击传说',
                description: '达到50连击',
                icon: '👑',
                rarity: 'legendary',
                condition: (stats) => stats.maxCombo >= 50
            },
            'coin_millionaire': {
                name: '金币百万富翁',
                description: '收集1000枚金币',
                icon: '🏆',
                rarity: 'epic',
                condition: (stats) => stats.totalCoins >= 1000
            },
            'jump_master': {
                name: '跳跃大师',
                description: '跳跃1000次',
                icon: '🎯',
                rarity: 'rare',
                condition: (stats) => stats.totalJumps >= 1000
            }
        };
    }

    updateStats(statName, value) {
        if (this.stats.hasOwnProperty(statName)) {
            if (statName === 'maxCombo' || statName === 'fastestLevel') {
                // 对于最大值统计，只在新值更好时更新
                if (statName === 'maxCombo' && value > this.stats[statName]) {
                    this.stats[statName] = value;
                } else if (statName === 'fastestLevel' && (this.stats[statName] === 0 || value < this.stats[statName])) {
                    this.stats[statName] = value;
                }
            } else {
                this.stats[statName] += value;
            }
            
            this.checkAchievements();
            this.saveProgress();
        }
    }

    checkAchievements() {
        for (let [id, achievement] of Object.entries(this.achievements)) {
            if (!this.unlockedAchievements.has(id) && achievement.condition(this.stats)) {
                this.unlockAchievement(id);
            }
        }
    }

    unlockAchievement(id) {
        if (this.unlockedAchievements.has(id)) return;
        
        this.unlockedAchievements.add(id);
        const achievement = this.achievements[id];
        
        // 添加通知
        this.notifications.push({
            achievement: achievement,
            id: id,
            time: Date.now(),
            duration: 5000, // 5秒
            y: -100 // 从屏幕上方滑入
        });
        
        // 播放成就音效（如果有的话）
        // audioManager.playSound('achievement');
        
        this.saveProgress();
    }

    updateNotifications() {
        const currentTime = Date.now();
        
        for (let i = this.notifications.length - 1; i >= 0; i--) {
            const notification = this.notifications[i];
            const elapsed = currentTime - notification.time;
            
            if (elapsed > notification.duration) {
                this.notifications.splice(i, 1);
            } else {
                // 动画效果
                const progress = Math.min(elapsed / 500, 1); // 0.5秒滑入动画
                notification.y = -100 + (progress * 120); // 滑入到y=20的位置
                
                if (elapsed > notification.duration - 500) {
                    // 滑出动画
                    const fadeProgress = (elapsed - (notification.duration - 500)) / 500;
                    notification.y = 20 - (fadeProgress * 120);
                }
            }
        }
    }

    drawNotifications(ctx, canvas) {
        this.notifications.forEach((notification, index) => {
            this.drawAchievementNotification(ctx, canvas, notification, index);
        });
    }

    drawAchievementNotification(ctx, canvas, notification, index) {
        const x = canvas.width / 2 - 200;
        const y = notification.y + (index * 80);
        const width = 400;
        const height = 70;
        
        ctx.save();
        
        // 背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
        ctx.fillRect(x, y, width, height);
        
        // 边框（根据稀有度变色）
        const rarityColors = {
            common: '#FFFFFF',
            uncommon: '#00FF00',
            rare: '#0080FF',
            epic: '#8000FF',
            legendary: '#FFD700'
        };
        
        ctx.strokeStyle = rarityColors[notification.achievement.rarity] || '#FFFFFF';
        ctx.lineWidth = 3;
        ctx.strokeRect(x, y, width, height);
        
        // 成就图标
        ctx.font = '32px Arial';
        ctx.fillText(notification.achievement.icon, x + 10, y + 45);
        
        // 成就解锁文字
        ctx.fillStyle = '#FFD700';
        ctx.font = '12px "Press Start 2P"';
        ctx.textAlign = 'left';
        ctx.fillText('成就解锁!', x + 60, y + 20);
        
        // 成就名称
        ctx.fillStyle = rarityColors[notification.achievement.rarity];
        ctx.font = '14px "Press Start 2P"';
        ctx.fillText(notification.achievement.name, x + 60, y + 40);
        
        // 成就描述
        ctx.fillStyle = '#CCCCCC';
        ctx.font = '10px "Press Start 2P"';
        ctx.fillText(notification.achievement.description, x + 60, y + 55);
        
        ctx.restore();
    }

    drawAchievementPanel(ctx, canvas) {
        // 这个方法可以用来绘制成就面板（按Tab键显示）
        const panelWidth = 600;
        const panelHeight = 400;
        const x = (canvas.width - panelWidth) / 2;
        const y = (canvas.height - panelHeight) / 2;
        
        ctx.save();
        
        // 背景
        ctx.fillStyle = 'rgba(0, 0, 0, 0.9)';
        ctx.fillRect(x, y, panelWidth, panelHeight);
        
        // 边框
        ctx.strokeStyle = '#00FF00';
        ctx.lineWidth = 2;
        ctx.strokeRect(x, y, panelWidth, panelHeight);
        
        // 标题
        ctx.fillStyle = '#FFD700';
        ctx.font = '20px "Press Start 2P"';
        ctx.textAlign = 'center';
        ctx.fillText('成就系统', x + panelWidth / 2, y + 30);
        
        // 统计信息
        ctx.fillStyle = '#FFFFFF';
        ctx.font = '12px "Press Start 2P"';
        ctx.textAlign = 'left';
        
        const statsY = y + 60;
        const statsLeft = x + 20;
        const statsRight = x + panelWidth / 2 + 20;
        
        ctx.fillText(`总跳跃次数: ${this.stats.totalJumps}`, statsLeft, statsY);
        ctx.fillText(`收集金币: ${this.stats.totalCoins}`, statsRight, statsY);
        ctx.fillText(`击败敌人: ${this.stats.totalEnemiesKilled}`, statsLeft, statsY + 20);
        ctx.fillText(`死亡次数: ${this.stats.totalDeaths}`, statsRight, statsY + 20);
        ctx.fillText(`最高连击: ${this.stats.maxCombo}`, statsLeft, statsY + 40);
        ctx.fillText(`完成关卡: ${this.stats.levelsCompleted}`, statsRight, statsY + 40);
        
        // 成就列表
        const achievementY = y + 140;
        const achievementHeight = 30;
        let currentY = achievementY;
        let count = 0;
        
        ctx.fillStyle = '#CCCCCC';
        ctx.font = '14px "Press Start 2P"';
        ctx.textAlign = 'center';
        ctx.fillText(`已解锁成就: ${this.unlockedAchievements.size}/${Object.keys(this.achievements).length}`, 
                    x + panelWidth / 2, achievementY - 10);
        
        for (let [id, achievement] of Object.entries(this.achievements)) {
            if (count >= 8) break; // 只显示前8个成就
            
            const isUnlocked = this.unlockedAchievements.has(id);
            const achievementX = x + 20 + (count % 2) * 280;
            const achievementYPos = currentY + Math.floor(count / 2) * achievementHeight;
            
            // 成就背景
            ctx.fillStyle = isUnlocked ? 'rgba(0, 255, 0, 0.2)' : 'rgba(100, 100, 100, 0.2)';
            ctx.fillRect(achievementX, achievementYPos, 260, achievementHeight - 5);
            
            // 成就图标和文字
            ctx.font = '16px Arial';
            ctx.fillText(achievement.icon, achievementX + 5, achievementYPos + 20);
            
            ctx.fillStyle = isUnlocked ? '#00FF00' : '#666666';
            ctx.font = '10px "Press Start 2P"';
            ctx.textAlign = 'left';
            ctx.fillText(achievement.name, achievementX + 30, achievementYPos + 15);
            
            count++;
        }
        
        ctx.restore();
    }

    saveProgress() {
        try {
            const data = {
                unlockedAchievements: Array.from(this.unlockedAchievements),
                stats: this.stats
            };
            localStorage.setItem('pixelJumpAchievements', JSON.stringify(data));
        } catch (e) {
            console.warn('无法保存成就进度:', e);
        }
    }

    loadProgress() {
        try {
            const data = localStorage.getItem('pixelJumpAchievements');
            if (data) {
                const parsed = JSON.parse(data);
                this.unlockedAchievements = new Set(parsed.unlockedAchievements || []);
                this.stats = { ...this.stats, ...parsed.stats };
            }
        } catch (e) {
            console.warn('无法加载成就进度:', e);
        }
    }

    getUnlockedCount() {
        return this.unlockedAchievements.size;
    }

    getTotalCount() {
        return Object.keys(this.achievements).length;
    }

    getCompletionPercentage() {
        return Math.round((this.getUnlockedCount() / this.getTotalCount()) * 100);
    }
}
