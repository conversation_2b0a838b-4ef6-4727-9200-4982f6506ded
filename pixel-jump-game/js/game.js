// 主游戏类
class Game {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.keys = {};
        this.gameState = 'menu'; // menu, playing, paused, gameOver, levelComplete

        // 游戏对象
        this.levelManager = new LevelManager();
        this.player = null;
        this.platforms = [];
        this.enemies = [];
        this.collectibles = [];
        this.spikes = [];
        this.particleSystem = new ParticleSystem();
        this.backgroundParticles = new BackgroundParticles(this.canvas);
        this.audioManager = new AudioManager();
        this.powerUpManager = new PowerUpManager();
        this.comboSystem = new ComboSystem();
        this.achievementSystem = new AchievementSystem();
        this.specialEffects = new SpecialEffectsManager();
        this.boss = null;
        this.hazards = [];

        // 游戏状态
        this.score = 0;
        this.lives = 3;
        this.camera = { x: 0, y: 0 };
        this.levelStartTime = 0;
        this.gameTime = 0;

        // UI元素
        this.livesElement = document.getElementById('lives');
        this.scoreElement = document.getElementById('score');
        this.levelElement = document.getElementById('level');

        this.init();
    }

    init() {
        this.setupEventListeners();
        this.setupMenus();
        this.gameLoop();
    }

    setupEventListeners() {
        // 键盘事件
        document.addEventListener('keydown', (e) => {
            this.keys[e.key] = true;

            // 重新开始关卡
            if (e.key === 'r' || e.key === 'R') {
                if (this.gameState === 'playing') {
                    this.restartLevel();
                }
            }

            // 暂停游戏
            if (e.key === 'Escape') {
                if (this.gameState === 'playing') {
                    this.gameState = 'paused';
                } else if (this.gameState === 'paused') {
                    this.gameState = 'playing';
                }
            }

            // 特殊技能
            if (this.gameState === 'playing' && this.player) {
                this.handleSpecialMoves(e.key);
            }
        });

        document.addEventListener('keyup', (e) => {
            this.keys[e.key] = false;
        });

        // 防止空格键滚动页面
        document.addEventListener('keydown', (e) => {
            if (e.key === ' ') {
                e.preventDefault();
            }
        });
    }

    handleSpecialMoves(key) {
        let moveType = null;
        switch (key.toLowerCase()) {
            case 'q': moveType = 'dash'; break;
            case 'e': moveType = 'ground_pound'; break;
            case 'f': moveType = 'time_slow'; break;
            case 'c': moveType = 'shield_burst'; break;
        }

        if (moveType && this.comboSystem.canUseSpecialMove(moveType)) {
            const effect = this.comboSystem.useSpecialMove(moveType);
            if (effect) {
                this.executeSpecialMove(effect);
            }
        }
    }

    executeSpecialMove(effect) {
        switch (effect.type) {
            case 'dash':
                this.player.vx = this.player.facing * 15;
                this.player.invulnerable = true;
                this.player.invulnerableTime = effect.duration;
                this.specialEffects.addEffect('screen_shake', { intensity: 3, duration: 10 });
                break;
            case 'ground_pound':
                this.player.vy = 20;
                this.specialEffects.addEffect('screen_shake', { intensity: 8, duration: 20 });
                // 伤害附近敌人
                this.enemies.forEach(enemy => {
                    const distance = Math.sqrt(
                        Math.pow(enemy.x - this.player.x, 2) +
                        Math.pow(enemy.y - this.player.y, 2)
                    );
                    if (distance < effect.range) {
                        enemy.takeDamage();
                        this.particleSystem.createExplosion(enemy.x + enemy.width/2, enemy.y + enemy.height/2, '#FFD700');
                    }
                });
                break;
            case 'time_slow':
                this.specialEffects.addEffect('time_slow', effect);
                break;
            case 'shield_burst':
                this.specialEffects.addEffect('shield_burst', effect);
                this.player.invulnerable = true;
                this.player.invulnerableTime = effect.duration;
                break;
        }
    }

    setupMenus() {
        // 开始游戏按钮
        document.getElementById('startBtn').addEventListener('click', () => {
            this.audioManager.resumeAudioContext();
            this.audioManager.playSound('menuSelect');
            this.startGame();
        });

        // 游戏说明按钮
        document.getElementById('instructionsBtn').addEventListener('click', () => {
            this.audioManager.playSound('menuSelect');
            document.getElementById('gameMenu').classList.add('hidden');
            document.getElementById('instructions').classList.remove('hidden');
        });

        // 返回按钮
        document.getElementById('backBtn').addEventListener('click', () => {
            this.audioManager.playSound('menuSelect');
            document.getElementById('instructions').classList.add('hidden');
            document.getElementById('gameMenu').classList.remove('hidden');
        });

        // 重新开始按钮
        document.getElementById('restartBtn').addEventListener('click', () => {
            this.audioManager.playSound('menuSelect');
            this.restartGame();
        });

        // 返回菜单按钮
        document.getElementById('menuBtn').addEventListener('click', () => {
            this.audioManager.playSound('menuSelect');
            this.returnToMenu();
        });

        // 下一关按钮
        document.getElementById('nextLevelBtn').addEventListener('click', () => {
            this.audioManager.playSound('menuSelect');
            this.nextLevel();
        });
    }

    startGame() {
        this.gameState = 'playing';
        this.score = 0;
        this.lives = 3;
        this.levelManager.currentLevel = 1;
        this.loadLevel();
        this.hideAllMenus();
        this.updateUI();
    }

    loadLevel() {
        const levelData = this.levelManager.getCurrentLevel();

        // 设置背景色
        this.canvas.style.background = levelData.background;

        // 创建玩家
        this.player = new Player(levelData.playerStart.x, levelData.playerStart.y);

        // 创建平台
        this.platforms = levelData.platforms.map(platformData => new Platform(platformData));

        // 创建敌人
        this.enemies = levelData.enemies.map(enemyData =>
            EnemyFactory.createEnemy(enemyData.type, enemyData.x, enemyData.y)
        );

        // 创建收集品
        this.collectibles = levelData.collectibles.map(collectibleData =>
            new Collectible(collectibleData)
        );

        // 创建尖刺
        this.spikes = levelData.spikes.map(spikeData => new Spike(spikeData));

        // 创建道具
        this.powerUpManager.reset();
        if (levelData.powerUps) {
            levelData.powerUps.forEach(powerUpData => {
                this.powerUpManager.addPowerUpSpawn(powerUpData.x, powerUpData.y, powerUpData.type);
            });
        }

        // 创建Boss
        this.boss = null;
        if (levelData.boss) {
            this.boss = new Boss(levelData.boss.x, levelData.boss.y, levelData.boss.type, levelData.boss.health);
        }

        // 创建危险元素
        this.hazards = [];
        if (levelData.hazards) {
            this.hazards = levelData.hazards.map(hazardData => new Hazard(hazardData));
        }

        // 重置相机
        this.camera.x = 0;
        this.camera.y = 0;

        this.levelStartTime = Date.now();
    }

    update() {
        if (this.gameState !== 'playing') return;

        this.gameTime++;

        // 更新系统
        this.comboSystem.update();
        this.achievementSystem.updateNotifications();
        this.specialEffects.update();

        // 更新背景粒子
        this.backgroundParticles.update();

        // 更新玩家
        const collisionResult = this.player.update(
            this.keys,
            this.platforms,
            this.enemies,
            this.collectibles,
            this.spikes,
            this.particleSystem,
            this.audioManager
        );

        // 处理碰撞结果
        if (collisionResult) {
            if (collisionResult.type === 'coin') {
                const comboBonus = this.comboSystem.addCombo('coin');
                this.score += collisionResult.points + comboBonus;
                this.audioManager.playSound('coin');
                this.achievementSystem.updateStats('totalCoins', 1);
                this.updateUI();
            } else if (collisionResult.type === 'enemy') {
                const comboBonus = this.comboSystem.addCombo('enemy');
                this.score += collisionResult.points + comboBonus;
                this.audioManager.playSound('enemyHit');
                this.achievementSystem.updateStats('totalEnemiesKilled', 1);
                this.updateUI();
            }
        }

        // 检查玩家是否受伤
        if (this.player.invulnerable && this.player.invulnerableTime === this.player.maxInvulnerableTime - 1) {
            this.lives--;
            this.audioManager.playSound('hurt');
            this.achievementSystem.updateStats('totalDeaths', 1);
            this.comboSystem.resetCombo();
            this.specialEffects.addEffect('screen_shake', { intensity: 10, duration: 30 });
            this.updateUI();

            if (this.lives <= 0) {
                this.gameOver();
                return;
            }
        }

        // 更新平台
        this.platforms.forEach(platform => {
            platform.update();
            // 检查玩家是否踩到消失平台
            if (platform.type === 'disappearing' && this.player.intersects(platform) && this.player.vy >= 0) {
                platform.onPlayerTouch();
            }
        });

        // 更新敌人
        for (let i = this.enemies.length - 1; i >= 0; i--) {
            const shouldRemove = this.enemies[i].update(this.platforms);
            if (shouldRemove) {
                this.enemies.splice(i, 1);
            }
        }

        // 更新收集品
        this.collectibles.forEach(collectible => collectible.update());

        // 更新道具系统
        this.powerUpManager.update(this.player, this.collectibles, this.particleSystem, this.audioManager);

        // 更新Boss
        if (this.boss) {
            const bossRemoved = this.boss.update(this.player, this.platforms, this.particleSystem, this.audioManager);
            if (bossRemoved) {
                this.boss = null;
                this.achievementSystem.updateStats('bossesDefeated', 1);
                this.score += 5000;
                this.specialEffects.addEffect('screen_shake', { intensity: 15, duration: 60 });
            }
        }

        // 更新危险元素
        this.hazards.forEach(hazard => hazard.update());

        // 更新粒子系统
        this.particleSystem.update();

        // 更新相机
        this.updateCamera();

        // 检查是否到达终点
        this.checkLevelComplete();

        // 检查是否掉出地图
        if (this.player.y > this.canvas.height + 100) {
            this.player.takeDamage(this.particleSystem);
        }
    }

    updateCamera() {
        // 跟随玩家的相机
        const targetX = this.player.x - this.canvas.width / 2;
        const targetY = this.player.y - this.canvas.height / 2;

        // 平滑相机移动
        this.camera.x += (targetX - this.camera.x) * 0.1;
        this.camera.y += (targetY - this.camera.y) * 0.1;

        // 限制相机边界
        this.camera.x = Math.max(0, Math.min(this.camera.x, 1200 - this.canvas.width));
        this.camera.y = Math.max(-200, Math.min(this.camera.y, 600 - this.canvas.height));
    }

    checkLevelComplete() {
        const levelData = this.levelManager.getCurrentLevel();
        const goal = levelData.goal;

        if (this.player.intersects(goal)) {
            this.levelComplete();
        }
    }

    draw() {
        // 清空画布
        this.ctx.clearRect(0, 0, this.canvas.width, this.canvas.height);

        // 保存上下文
        this.ctx.save();

        // 应用屏幕震动
        this.specialEffects.applyScreenShake(this.ctx);

        // 应用相机变换
        this.ctx.translate(-this.camera.x, -this.camera.y);

        // 绘制背景粒子
        this.backgroundParticles.draw(this.ctx);

        // 绘制平台
        this.platforms.forEach(platform => platform.draw(this.ctx));

        // 绘制尖刺
        this.spikes.forEach(spike => spike.draw(this.ctx));

        // 绘制危险元素
        this.hazards.forEach(hazard => hazard.draw(this.ctx));

        // 绘制收集品
        this.collectibles.forEach(collectible => collectible.draw(this.ctx));

        // 绘制道具
        this.powerUpManager.draw(this.ctx);

        // 绘制敌人
        this.enemies.forEach(enemy => enemy.draw(this.ctx));

        // 绘制Boss
        if (this.boss) {
            this.boss.draw(this.ctx);
        }

        // 绘制玩家
        if (this.player) {
            this.player.draw(this.ctx);
        }

        // 绘制终点
        this.drawGoal();

        // 绘制粒子效果
        this.particleSystem.draw(this.ctx);

        // 恢复上下文
        this.ctx.restore();

        // 绘制UI（不受相机影响）
        this.drawUI();

        // 绘制道具UI
        this.powerUpManager.drawUI(this.ctx, this.canvas);

        // 绘制连击系统UI
        this.comboSystem.drawComboUI(this.ctx, this.canvas);
        this.comboSystem.drawEnergyUI(this.ctx, this.canvas);
        this.comboSystem.drawSpecialMoveHints(this.ctx, this.canvas);

        // 绘制特殊效果
        this.specialEffects.drawTimeSlowEffect(this.ctx, this.canvas);
        this.specialEffects.drawShieldBurstEffect(this.ctx, this.player);

        // 绘制成就通知
        this.achievementSystem.drawNotifications(this.ctx, this.canvas);
    }

    drawGoal() {
        const levelData = this.levelManager.getCurrentLevel();
        const goal = levelData.goal;

        // 终点旗帜
        this.ctx.fillStyle = '#FFD700';
        this.ctx.fillRect(goal.x, goal.y, goal.width, goal.height);

        // 旗帜图案
        this.ctx.fillStyle = '#FF6B6B';
        this.ctx.fillRect(goal.x + 5, goal.y, 20, 15);

        // 旗杆
        this.ctx.fillStyle = '#8B4513';
        this.ctx.fillRect(goal.x + 2, goal.y, 4, goal.height);

        // 发光效果
        const glowIntensity = Math.sin(Date.now() * 0.01) * 0.3 + 0.7;
        this.ctx.fillStyle = `rgba(255, 215, 0, ${glowIntensity * 0.3})`;
        this.ctx.fillRect(goal.x - 5, goal.y - 5, goal.width + 10, goal.height + 10);
    }

    drawUI() {
        if (this.gameState === 'paused') {
            this.ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);

            this.ctx.fillStyle = '#00ff00';
            this.ctx.font = '24px "Press Start 2P"';
            this.ctx.textAlign = 'center';
            this.ctx.fillText('游戏暂停', this.canvas.width / 2, this.canvas.height / 2);
            this.ctx.font = '12px "Press Start 2P"';
            this.ctx.fillText('按 ESC 继续', this.canvas.width / 2, this.canvas.height / 2 + 40);
        }
    }

    gameLoop() {
        this.update();
        this.draw();
        requestAnimationFrame(() => this.gameLoop());
    }

    // 游戏状态管理方法
    restartLevel() {
        this.loadLevel();
    }

    restartGame() {
        this.hideAllMenus();
        this.startGame();
    }

    returnToMenu() {
        this.gameState = 'menu';
        this.hideAllMenus();
        document.getElementById('gameMenu').classList.remove('hidden');
    }

    nextLevel() {
        if (this.levelManager.nextLevel()) {
            this.loadLevel();
            this.hideAllMenus();
            this.gameState = 'playing';
            this.updateUI();
        } else {
            // 游戏完成
            this.gameComplete();
        }
    }

    levelComplete() {
        this.gameState = 'levelComplete';
        this.audioManager.playSound('levelComplete');
        const timeBonus = Math.max(0, 1000 - Math.floor((Date.now() - this.levelStartTime) / 100));
        this.score += timeBonus;

        // 更新成就统计
        this.achievementSystem.updateStats('levelsCompleted', 1);
        this.achievementSystem.updateStats('maxCombo', this.comboSystem.maxCombo);

        const levelTime = Math.floor((Date.now() - this.levelStartTime) / 1000);
        this.achievementSystem.updateStats('fastestLevel', levelTime);

        document.getElementById('completedLevel').textContent = this.levelManager.currentLevel;
        document.getElementById('levelScore').textContent = timeBonus;
        document.getElementById('levelComplete').classList.remove('hidden');
        this.updateUI();
    }

    gameOver() {
        this.gameState = 'gameOver';
        this.audioManager.playSound('gameOver');
        document.getElementById('finalScore').textContent = this.score;
        document.getElementById('finalLevel').textContent = this.levelManager.currentLevel;
        document.getElementById('gameOver').classList.remove('hidden');
    }

    gameComplete() {
        // 所有关卡完成
        alert('恭喜！你完成了所有关卡！\n最终分数: ' + this.score);
        this.returnToMenu();
    }

    updateUI() {
        this.livesElement.textContent = this.lives;
        this.scoreElement.textContent = this.score;
        this.levelElement.textContent = this.levelManager.currentLevel;
    }

    hideAllMenus() {
        document.getElementById('gameMenu').classList.add('hidden');
        document.getElementById('gameOver').classList.add('hidden');
        document.getElementById('levelComplete').classList.add('hidden');
        document.getElementById('instructions').classList.add('hidden');
    }
}

// 启动游戏
window.addEventListener('load', () => {
    new Game();
});
