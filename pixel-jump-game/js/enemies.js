// 敌人基类
class Enemy {
    constructor(x, y, type) {
        this.x = x;
        this.y = y;
        this.type = type;
        this.width = 20;
        this.height = 20;
        this.health = 1;
        this.speed = 1;
        this.direction = 1;
        this.animFrame = 0;
        this.animTimer = 0;
        this.dead = false;
        this.deathTimer = 0;
    }

    update(platforms) {
        if (this.dead) {
            this.deathTimer++;
            return this.deathTimer > 30; // 30帧后移除
        }

        this.move();
        this.checkPlatformCollisions(platforms);
        this.updateAnimation();
        return false;
    }

    move() {
        this.x += this.speed * this.direction;
    }

    checkPlatformCollisions(platforms) {
        // 检查是否到达平台边缘或撞墙
        let onPlatform = false;
        let hitWall = false;

        platforms.forEach(platform => {
            // 检查是否在平台上
            if (this.x + this.width > platform.x &&
                this.x < platform.x + platform.width &&
                this.y + this.height >= platform.y &&
                this.y + this.height <= platform.y + 10) {
                onPlatform = true;
            }

            // 检查墙壁碰撞
            if (this.intersects(platform)) {
                if (this.direction > 0 && this.x < platform.x) {
                    hitWall = true;
                } else if (this.direction < 0 && this.x > platform.x) {
                    hitWall = true;
                }
            }
        });

        // 如果到达边缘或撞墙，转向
        if (!onPlatform || hitWall) {
            this.direction *= -1;
        }
    }

    takeDamage() {
        this.health--;
        if (this.health <= 0) {
            this.dead = true;
        }
    }

    intersects(rect) {
        return this.x < rect.x + rect.width &&
               this.x + this.width > rect.x &&
               this.y < rect.y + rect.height &&
               this.y + this.height > rect.y;
    }

    updateAnimation() {
        this.animTimer++;
        if (this.animTimer >= 15) {
            this.animTimer = 0;
            this.animFrame = (this.animFrame + 1) % 2;
        }
    }

    draw(ctx) {
        if (this.dead) {
            // 死亡动画
            ctx.save();
            ctx.globalAlpha = 1 - (this.deathTimer / 30);
            ctx.translate(this.x + this.width/2, this.y + this.height/2);
            ctx.rotate(this.deathTimer * 0.2);
            ctx.translate(-this.width/2, -this.height/2);
        }

        this.drawEnemy(ctx);

        if (this.dead) {
            ctx.restore();
        }
    }

    drawEnemy(ctx) {
        // 基础敌人绘制，子类重写
        ctx.fillStyle = '#ff4444';
        ctx.fillRect(this.x, this.y, this.width, this.height);
    }
}

// 巡逻敌人
class PatrolEnemy extends Enemy {
    constructor(x, y) {
        super(x, y, 'patrol');
        this.speed = 1.5;
        this.width = 24;
        this.height = 24;
    }

    drawEnemy(ctx) {
        // 身体
        ctx.fillStyle = '#ff4444';
        ctx.fillRect(this.x + 2, this.y + 8, 20, 16);

        // 头部
        ctx.fillStyle = '#ff6666';
        ctx.fillRect(this.x + 4, this.y, 16, 12);

        // 眼睛
        ctx.fillStyle = '#000';
        ctx.fillRect(this.x + 6, this.y + 3, 3, 3);
        ctx.fillRect(this.x + 15, this.y + 3, 3, 3);

        // 腿部动画
        if (this.animFrame === 0) {
            ctx.fillStyle = '#cc3333';
            ctx.fillRect(this.x + 4, this.y + 20, 6, 4);
            ctx.fillRect(this.x + 14, this.y + 20, 6, 4);
        } else {
            ctx.fillStyle = '#cc3333';
            ctx.fillRect(this.x + 6, this.y + 20, 6, 4);
            ctx.fillRect(this.x + 12, this.y + 20, 6, 4);
        }

        // 方向指示
        if (this.direction > 0) {
            ctx.fillStyle = '#ffff00';
            ctx.fillRect(this.x + 20, this.y + 6, 2, 2);
        } else {
            ctx.fillStyle = '#ffff00';
            ctx.fillRect(this.x + 2, this.y + 6, 2, 2);
        }
    }
}

// 跳跃敌人
class JumpEnemy extends Enemy {
    constructor(x, y) {
        super(x, y, 'jump');
        this.speed = 0;
        this.vy = 0;
        this.gravity = 0.3;
        this.jumpPower = 8;
        this.jumpTimer = 0;
        this.jumpInterval = 120; // 2秒跳一次
        this.onGround = false;
        this.width = 20;
        this.height = 20;
    }

    move() {
        // 跳跃逻辑
        this.jumpTimer++;
        if (this.jumpTimer >= this.jumpInterval && this.onGround) {
            this.vy = -this.jumpPower;
            this.onGround = false;
            this.jumpTimer = 0;
        }

        // 重力
        this.vy += this.gravity;
        this.y += this.vy;
    }

    checkPlatformCollisions(platforms) {
        this.onGround = false;

        platforms.forEach(platform => {
            if (this.intersects(platform)) {
                if (this.vy > 0 && this.y < platform.y) {
                    this.y = platform.y - this.height;
                    this.vy = 0;
                    this.onGround = true;
                }
            }
        });
    }

    drawEnemy(ctx) {
        // 身体（圆形）
        ctx.fillStyle = '#44ff44';
        ctx.beginPath();
        ctx.arc(this.x + this.width/2, this.y + this.height/2, this.width/2 - 2, 0, Math.PI * 2);
        ctx.fill();

        // 眼睛
        ctx.fillStyle = '#000';
        ctx.fillRect(this.x + 6, this.y + 6, 3, 3);
        ctx.fillRect(this.x + 11, this.y + 6, 3, 3);

        // 嘴巴
        ctx.fillStyle = '#000';
        ctx.fillRect(this.x + 7, this.y + 12, 6, 2);

        // 跳跃状态指示
        if (!this.onGround) {
            ctx.fillStyle = '#88ff88';
            ctx.fillRect(this.x + 2, this.y + this.height - 2, this.width - 4, 2);
        }
    }
}

// 飞行敌人
class FlyEnemy extends Enemy {
    constructor(x, y) {
        super(x, y, 'fly');
        this.speed = 2;
        this.amplitude = 30; // 上下移动幅度
        this.frequency = 0.05; // 上下移动频率
        this.baseY = y;
        this.time = 0;
        this.width = 18;
        this.height = 18;
    }

    move() {
        this.x += this.speed * this.direction;
        this.time += this.frequency;
        this.y = this.baseY + Math.sin(this.time) * this.amplitude;
    }

    checkPlatformCollisions(platforms) {
        // 飞行敌人只检查左右边界
        platforms.forEach(platform => {
            if (this.x + this.width > platform.x + platform.width + 50 ||
                this.x < platform.x - 50) {
                this.direction *= -1;
            }
        });
    }

    drawEnemy(ctx) {
        // 身体
        ctx.fillStyle = '#ff44ff';
        ctx.fillRect(this.x + 3, this.y + 6, 12, 8);

        // 翅膀动画
        const wingFlap = Math.sin(this.time * 10) > 0;
        ctx.fillStyle = '#ffaaff';

        if (wingFlap) {
            // 翅膀向上
            ctx.fillRect(this.x, this.y + 2, 4, 6);
            ctx.fillRect(this.x + 14, this.y + 2, 4, 6);
        } else {
            // 翅膀向下
            ctx.fillRect(this.x, this.y + 8, 4, 6);
            ctx.fillRect(this.x + 14, this.y + 8, 4, 6);
        }

        // 眼睛
        ctx.fillStyle = '#000';
        ctx.fillRect(this.x + 5, this.y + 7, 2, 2);
        ctx.fillRect(this.x + 11, this.y + 7, 2, 2);

        // 触角
        ctx.fillStyle = '#aa00aa';
        ctx.fillRect(this.x + 6, this.y + 2, 1, 3);
        ctx.fillRect(this.x + 11, this.y + 2, 1, 3);
        ctx.fillRect(this.x + 5, this.y, 1, 2);
        ctx.fillRect(this.x + 12, this.y, 1, 2);
    }
}

// 射击敌人
class ShooterEnemy extends Enemy {
    constructor(x, y) {
        super(x, y, 'shooter');
        this.speed = 0.5;
        this.shootTimer = 0;
        this.shootInterval = 180; // 3秒
        this.projectiles = [];
        this.detectionRange = 200;
        this.width = 28;
        this.height = 28;
    }

    update(platforms) {
        const shouldRemove = super.update(platforms);
        if (shouldRemove) return true;

        // 更新投射物
        for (let i = this.projectiles.length - 1; i >= 0; i--) {
            this.projectiles[i].update();
            if (this.projectiles[i].shouldRemove()) {
                this.projectiles.splice(i, 1);
            }
        }

        return false;
    }

    move() {
        // 射击敌人移动较慢
        this.x += this.speed * this.direction;
        this.shootTimer++;
    }

    canSeePlayer(player) {
        const distance = Math.sqrt(
            Math.pow(player.x - this.x, 2) + Math.pow(player.y - this.y, 2)
        );
        return distance <= this.detectionRange;
    }

    shootAtPlayer(player) {
        if (this.shootTimer >= this.shootInterval && this.canSeePlayer(player)) {
            const angle = Math.atan2(player.y - this.y, player.x - this.x);
            this.projectiles.push(new EnemyProjectile(
                this.x + this.width / 2,
                this.y + this.height / 2,
                Math.cos(angle) * 3,
                Math.sin(angle) * 3
            ));
            this.shootTimer = 0;
        }
    }

    drawEnemy(ctx) {
        // 身体
        ctx.fillStyle = '#8B008B';
        ctx.fillRect(this.x + 2, this.y + 8, 24, 16);

        // 头部
        ctx.fillStyle = '#9932CC';
        ctx.fillRect(this.x + 4, this.y, 20, 12);

        // 眼睛
        ctx.fillStyle = '#FF0000';
        ctx.fillRect(this.x + 8, this.y + 3, 3, 3);
        ctx.fillRect(this.x + 17, this.y + 3, 3, 3);

        // 武器
        ctx.fillStyle = '#696969';
        if (this.direction > 0) {
            ctx.fillRect(this.x + 24, this.y + 10, 8, 4);
        } else {
            ctx.fillRect(this.x - 4, this.y + 10, 8, 4);
        }

        // 绘制投射物
        this.projectiles.forEach(projectile => projectile.draw(ctx));
    }
}

// 爆炸敌人
class ExplodeEnemy extends Enemy {
    constructor(x, y) {
        super(x, y, 'explode');
        this.speed = 2;
        this.detectionRange = 80;
        this.explosionRange = 60;
        this.isChasing = false;
        this.flashTimer = 0;
        this.explodeTimer = 0;
        this.explodeDelay = 90; // 1.5秒
        this.width = 22;
        this.height = 22;
    }

    update(platforms) {
        if (this.dead) {
            this.deathTimer++;
            return this.deathTimer > 60; // 1秒后移除
        }

        this.move();
        this.checkPlatformCollisions(platforms);
        this.updateAnimation();

        if (this.explodeTimer > 0) {
            this.explodeTimer--;
            this.flashTimer++;
            if (this.explodeTimer <= 0) {
                this.explode();
            }
        }

        return false;
    }

    move() {
        if (this.explodeTimer > 0) {
            // 爆炸倒计时中，停止移动
            return;
        }

        if (this.isChasing) {
            this.x += this.speed * this.direction * 1.5; // 追击时更快
        } else {
            this.x += this.speed * this.direction;
        }
    }

    startChasing(player) {
        if (!this.isChasing && !this.explodeTimer) {
            this.isChasing = true;
            this.direction = player.x > this.x ? 1 : -1;
            this.explodeTimer = this.explodeDelay;
        }
    }

    explode() {
        this.dead = true;
        this.deathTimer = 0;
        // 爆炸效果将在游戏主循环中处理
    }

    drawEnemy(ctx) {
        // 爆炸倒计时闪烁效果
        if (this.explodeTimer > 0 && Math.floor(this.flashTimer / 5) % 2) {
            ctx.fillStyle = '#FFFF00';
        } else {
            ctx.fillStyle = '#FF4500';
        }

        // 身体（圆形）
        ctx.beginPath();
        ctx.arc(this.x + this.width/2, this.y + this.height/2, this.width/2 - 2, 0, Math.PI * 2);
        ctx.fill();

        // 眼睛
        ctx.fillStyle = '#000';
        ctx.fillRect(this.x + 6, this.y + 6, 3, 3);
        ctx.fillRect(this.x + 13, this.y + 6, 3, 3);

        // 导火索
        if (this.explodeTimer > 0) {
            ctx.fillStyle = '#FF0000';
            ctx.fillRect(this.x + this.width/2 - 1, this.y - 5, 2, 5);

            // 火花效果
            ctx.fillStyle = '#FFFF00';
            ctx.fillRect(this.x + this.width/2 - 1, this.y - 8, 2, 3);
        }
    }
}

// 敌人投射物
class EnemyProjectile {
    constructor(x, y, vx, vy) {
        this.x = x;
        this.y = y;
        this.vx = vx;
        this.vy = vy;
        this.width = 8;
        this.height = 8;
        this.life = 300; // 5秒生命周期
    }

    update() {
        this.x += this.vx;
        this.y += this.vy;
        this.life--;
    }

    shouldRemove() {
        return this.life <= 0 || this.x < -50 || this.x > 1250 || this.y < -50 || this.y > 650;
    }

    intersects(rect) {
        return this.x < rect.x + rect.width &&
               this.x + this.width > rect.x &&
               this.y < rect.y + rect.height &&
               this.y + this.height > rect.y;
    }

    draw(ctx) {
        ctx.fillStyle = '#FF0000';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // 发光效果
        ctx.fillStyle = '#FFAA00';
        ctx.fillRect(this.x + 1, this.y + 1, this.width - 2, this.height - 2);
    }
}

// 危险元素类
class Hazard {
    constructor(data) {
        this.x = data.x;
        this.y = data.y;
        this.width = data.width;
        this.height = data.height;
        this.type = data.type;
        this.animFrame = 0;
        this.animTimer = 0;
        this.damage = data.damage || 1;
    }

    update() {
        this.animTimer++;
        if (this.animTimer >= 10) {
            this.animTimer = 0;
            this.animFrame = (this.animFrame + 1) % 4;
        }
    }

    draw(ctx) {
        switch (this.type) {
            case 'lava':
                this.drawLava(ctx);
                break;
            case 'acid':
                this.drawAcid(ctx);
                break;
            case 'electricity':
                this.drawElectricity(ctx);
                break;
        }
    }

    drawLava(ctx) {
        // 熔岩基础
        ctx.fillStyle = '#8B0000';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // 熔岩表面动画
        const bubbleOffset = Math.sin(Date.now() * 0.005 + this.x * 0.01) * 3;
        ctx.fillStyle = '#FF4500';
        ctx.fillRect(this.x, this.y + bubbleOffset, this.width, 8);

        // 气泡效果
        for (let i = 0; i < this.width; i += 20) {
            const bubbleY = this.y + Math.sin(Date.now() * 0.008 + i * 0.1) * 2;
            ctx.fillStyle = '#FFD700';
            ctx.fillRect(this.x + i + 5, bubbleY, 4, 4);
        }
    }

    drawAcid(ctx) {
        // 酸液
        ctx.fillStyle = '#32CD32';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // 酸液表面
        ctx.fillStyle = '#00FF00';
        ctx.fillRect(this.x, this.y, this.width, 6);

        // 腐蚀效果
        for (let i = 0; i < this.width; i += 15) {
            ctx.fillStyle = '#ADFF2F';
            ctx.fillRect(this.x + i, this.y + 2, 3, 3);
        }
    }

    drawElectricity(ctx) {
        // 电流基础
        ctx.fillStyle = '#4169E1';
        ctx.fillRect(this.x, this.y, this.width, this.height);

        // 电弧效果
        if (this.animFrame % 2 === 0) {
            ctx.strokeStyle = '#FFFF00';
            ctx.lineWidth = 2;
            ctx.beginPath();
            for (let i = 0; i < this.width; i += 10) {
                const startY = this.y + Math.random() * this.height;
                const endY = this.y + Math.random() * this.height;
                ctx.moveTo(this.x + i, startY);
                ctx.lineTo(this.x + i + 10, endY);
            }
            ctx.stroke();
        }
    }
}

// 敌人工厂
class EnemyFactory {
    static createEnemy(type, x, y) {
        switch (type) {
            case 'patrol':
                return new PatrolEnemy(x, y);
            case 'jump':
                return new JumpEnemy(x, y);
            case 'fly':
                return new FlyEnemy(x, y);
            case 'shooter':
                return new ShooterEnemy(x, y);
            case 'explode':
                return new ExplodeEnemy(x, y);
            default:
                return new PatrolEnemy(x, y);
        }
    }
}
