// 道具系统
class PowerUp {
    constructor(x, y, type) {
        this.x = x;
        this.y = y;
        this.width = 20;
        this.height = 20;
        this.type = type;
        this.animFrame = 0;
        this.animTimer = 0;
        this.floatOffset = Math.random() * Math.PI * 2;
        this.collected = false;
        this.duration = this.getDuration();
    }

    getDuration() {
        switch (this.type) {
            case 'speed': return 600; // 10秒
            case 'jump': return 600;
            case 'shield': return 300; // 5秒
            case 'magnet': return 900; // 15秒
            default: return 600;
        }
    }

    update() {
        this.animTimer++;
        if (this.animTimer >= 10) {
            this.animTimer = 0;
            this.animFrame = (this.animFrame + 1) % 4;
        }
    }

    draw(ctx) {
        const floatY = this.y + Math.sin(Date.now() * 0.005 + this.floatOffset) * 4;
        
        ctx.save();
        ctx.translate(this.x + this.width/2, floatY + this.height/2);
        ctx.rotate(this.animFrame * 0.1);
        
        switch (this.type) {
            case 'speed':
                this.drawSpeedPowerUp(ctx);
                break;
            case 'jump':
                this.drawJumpPowerUp(ctx);
                break;
            case 'shield':
                this.drawShieldPowerUp(ctx);
                break;
            case 'magnet':
                this.drawMagnetPowerUp(ctx);
                break;
        }
        
        ctx.restore();
        
        // 发光效果
        ctx.save();
        ctx.globalAlpha = 0.3;
        ctx.fillStyle = this.getGlowColor();
        ctx.fillRect(this.x - 2, floatY - 2, this.width + 4, this.height + 4);
        ctx.restore();
    }

    drawSpeedPowerUp(ctx) {
        // 速度提升道具 - 闪电
        ctx.fillStyle = '#FFFF00';
        ctx.fillRect(-8, -10, 4, 8);
        ctx.fillRect(-6, -6, 8, 4);
        ctx.fillRect(-4, -2, 4, 8);
        ctx.fillRect(0, 2, 6, 4);
        
        // 高光
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(-7, -8, 2, 4);
        ctx.fillRect(-2, 0, 2, 4);
    }

    drawJumpPowerUp(ctx) {
        // 跳跃提升道具 - 翅膀
        ctx.fillStyle = '#00FFFF';
        ctx.fillRect(-8, -6, 6, 3);
        ctx.fillRect(-6, -3, 8, 3);
        ctx.fillRect(-4, 0, 6, 3);
        ctx.fillRect(2, -6, 6, 3);
        ctx.fillRect(4, -3, 8, 3);
        ctx.fillRect(6, 0, 6, 3);
        
        // 羽毛细节
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(-6, -5, 1, 2);
        ctx.fillRect(-4, -2, 1, 2);
        ctx.fillRect(4, -5, 1, 2);
        ctx.fillRect(6, -2, 1, 2);
    }

    drawShieldPowerUp(ctx) {
        // 护盾道具
        ctx.fillStyle = '#00FF00';
        ctx.fillRect(-6, -8, 12, 4);
        ctx.fillRect(-8, -4, 16, 8);
        ctx.fillRect(-6, 4, 12, 4);
        
        // 十字图案
        ctx.fillStyle = '#FFFFFF';
        ctx.fillRect(-1, -6, 2, 12);
        ctx.fillRect(-6, -1, 12, 2);
    }

    drawMagnetPowerUp(ctx) {
        // 磁铁道具
        ctx.fillStyle = '#FF0000';
        ctx.fillRect(-8, -6, 4, 12);
        ctx.fillRect(4, -6, 4, 12);
        
        ctx.fillStyle = '#0000FF';
        ctx.fillRect(-6, -8, 12, 4);
        
        // 磁力线
        ctx.fillStyle = '#FFFF00';
        ctx.fillRect(-2, -10, 1, 2);
        ctx.fillRect(0, -10, 1, 2);
        ctx.fillRect(2, -10, 1, 2);
    }

    getGlowColor() {
        switch (this.type) {
            case 'speed': return '#FFFF00';
            case 'jump': return '#00FFFF';
            case 'shield': return '#00FF00';
            case 'magnet': return '#FF00FF';
            default: return '#FFFFFF';
        }
    }
}

// 道具管理器
class PowerUpManager {
    constructor() {
        this.activePowerUps = new Map();
        this.powerUpSpawns = [];
    }

    addPowerUpSpawn(x, y, type) {
        this.powerUpSpawns.push(new PowerUp(x, y, type));
    }

    update(player, collectibles, particleSystem, audioManager) {
        // 更新道具生成点
        this.powerUpSpawns.forEach(powerUp => powerUp.update());
        
        // 检查道具收集
        for (let i = this.powerUpSpawns.length - 1; i >= 0; i--) {
            const powerUp = this.powerUpSpawns[i];
            if (this.checkCollision(player, powerUp)) {
                this.collectPowerUp(player, powerUp, particleSystem, audioManager);
                this.powerUpSpawns.splice(i, 1);
            }
        }
        
        // 更新激活的道具效果
        for (let [type, data] of this.activePowerUps) {
            data.duration--;
            if (data.duration <= 0) {
                this.removePowerUp(player, type);
            }
        }
        
        // 磁铁效果
        if (this.activePowerUps.has('magnet')) {
            this.applyMagnetEffect(player, collectibles);
        }
    }

    checkCollision(player, powerUp) {
        return player.x < powerUp.x + powerUp.width &&
               player.x + player.width > powerUp.x &&
               player.y < powerUp.y + powerUp.height &&
               player.y + player.height > powerUp.y;
    }

    collectPowerUp(player, powerUp, particleSystem, audioManager) {
        // 播放收集音效
        audioManager.playSound('coin');
        
        // 创建粒子效果
        particleSystem.createExplosion(
            powerUp.x + powerUp.width/2, 
            powerUp.y + powerUp.height/2, 
            powerUp.getGlowColor(), 
            12
        );
        
        // 激活道具效果
        this.activatePowerUp(player, powerUp.type, powerUp.duration);
    }

    activatePowerUp(player, type, duration) {
        // 如果已有同类型道具，重置时间
        if (this.activePowerUps.has(type)) {
            this.activePowerUps.get(type).duration = duration;
            return;
        }

        // 应用道具效果
        const originalValues = {};
        
        switch (type) {
            case 'speed':
                originalValues.speed = player.speed;
                originalValues.acceleration = player.acceleration;
                player.speed *= 1.5;
                player.acceleration *= 1.3;
                break;
            case 'jump':
                originalValues.jumpPower = player.jumpPower;
                originalValues.gravity = player.gravity;
                player.jumpPower *= 1.3;
                player.gravity *= 0.8;
                break;
            case 'shield':
                originalValues.invulnerableTime = player.maxInvulnerableTime;
                player.maxInvulnerableTime *= 2;
                break;
            case 'magnet':
                // 磁铁效果在update中处理
                break;
        }
        
        this.activePowerUps.set(type, {
            duration: duration,
            originalValues: originalValues
        });
    }

    removePowerUp(player, type) {
        const data = this.activePowerUps.get(type);
        if (!data) return;
        
        // 恢复原始值
        switch (type) {
            case 'speed':
                player.speed = data.originalValues.speed;
                player.acceleration = data.originalValues.acceleration;
                break;
            case 'jump':
                player.jumpPower = data.originalValues.jumpPower;
                player.gravity = data.originalValues.gravity;
                break;
            case 'shield':
                player.maxInvulnerableTime = data.originalValues.invulnerableTime;
                break;
        }
        
        this.activePowerUps.delete(type);
    }

    applyMagnetEffect(player, collectibles) {
        const magnetRange = 100;
        const magnetForce = 0.3;
        
        collectibles.forEach(collectible => {
            const dx = (player.x + player.width/2) - (collectible.x + collectible.width/2);
            const dy = (player.y + player.height/2) - (collectible.y + collectible.height/2);
            const distance = Math.sqrt(dx * dx + dy * dy);
            
            if (distance < magnetRange && distance > 0) {
                const force = magnetForce * (1 - distance / magnetRange);
                collectible.x += (dx / distance) * force;
                collectible.y += (dy / distance) * force;
            }
        });
    }

    draw(ctx) {
        this.powerUpSpawns.forEach(powerUp => powerUp.draw(ctx));
    }

    drawUI(ctx, canvas) {
        let yOffset = 10;
        const iconSize = 24;
        const spacing = 30;
        
        for (let [type, data] of this.activePowerUps) {
            // 绘制道具图标
            ctx.save();
            ctx.translate(canvas.width - 40, yOffset + iconSize/2);
            
            // 背景
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(-iconSize/2 - 2, -iconSize/2 - 2, iconSize + 4, iconSize + 4);
            
            // 图标
            ctx.scale(0.6, 0.6);
            switch (type) {
                case 'speed':
                    ctx.fillStyle = '#FFFF00';
                    ctx.fillRect(-8, -10, 4, 8);
                    ctx.fillRect(-6, -6, 8, 4);
                    ctx.fillRect(-4, -2, 4, 8);
                    break;
                case 'jump':
                    ctx.fillStyle = '#00FFFF';
                    ctx.fillRect(-8, -6, 6, 3);
                    ctx.fillRect(2, -6, 6, 3);
                    break;
                case 'shield':
                    ctx.fillStyle = '#00FF00';
                    ctx.fillRect(-6, -8, 12, 16);
                    break;
                case 'magnet':
                    ctx.fillStyle = '#FF00FF';
                    ctx.fillRect(-8, -6, 4, 12);
                    ctx.fillRect(4, -6, 4, 12);
                    break;
            }
            
            ctx.restore();
            
            // 时间条
            const timeRatio = data.duration / this.getPowerUpMaxDuration(type);
            const barWidth = 30;
            const barHeight = 4;
            
            ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
            ctx.fillRect(canvas.width - 80, yOffset + iconSize + 2, barWidth, barHeight);
            
            ctx.fillStyle = this.getPowerUpColor(type);
            ctx.fillRect(canvas.width - 80, yOffset + iconSize + 2, barWidth * timeRatio, barHeight);
            
            yOffset += spacing;
        }
    }

    getPowerUpMaxDuration(type) {
        switch (type) {
            case 'speed': return 600;
            case 'jump': return 600;
            case 'shield': return 300;
            case 'magnet': return 900;
            default: return 600;
        }
    }

    getPowerUpColor(type) {
        switch (type) {
            case 'speed': return '#FFFF00';
            case 'jump': return '#00FFFF';
            case 'shield': return '#00FF00';
            case 'magnet': return '#FF00FF';
            default: return '#FFFFFF';
        }
    }

    reset() {
        this.activePowerUps.clear();
        this.powerUpSpawns = [];
    }
}
