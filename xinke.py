@tool
def generate_json(text='', history='', msg='', max_length=10):
    # 去除文本中的标点符号
    text = re.sub(r'[，。？！；：,.?!;:]', '', text)

    # 定义中断关键词列表
    interrupt_keywords = [
        '正在通话中', '留言', '超时', '机主', '无人接听',
        '转达', '来电秘书', '爱莫能助', '来电', '助理', '感谢', "很抱歉", '呼叫', '不方便接电话',
        '喂，你好，你有什么事？', '您继续说。', '你说这么多，就是想要还钱吧？',
        '那现在账户有产生罚息吗', '额度是实时恢复的吗？', '如果没有及时还款，会对征信造成影响吗？',
        '如果今天处理好，对征信会造成影响吗？', '需要缴纳滞纳金吗？', '请问您是哪位？',
        '想请问一下逾期利息的具体数额是多少', '还有其他需要补充的吗','秘书','转告'
    ]

    # 去除关键词中的标点符号
    processed_keywords = [re.sub(r'[，。？！；：,.?!;:]', '', keyword) for keyword in interrupt_keywords]

    # 兼容处理 \n 和 \\n
    history = history.replace('\\n', '\n')
    msg = msg.replace('\\n', '\n')

    # 提取并分离历史记录和客户当轮回复
    history_lines = history.strip().split("\n")
    msg_lines = msg.strip().split("\n")

    # 判断文本长度是否小于指定长度
    is_short_text = len(history) < max_length

    # 提炼历史记录
    extracted_history = "\n".join(history_lines[:-1])  # 去掉最后一行，因为它属于当轮回复

    # 提炼当轮回复并在最后追加提示信息
    extracted_msg = f"{history_lines[-1]}\n{msg_lines[-1]}    // 注意！ 主要就根据这句进行识别意图。"

    # 初始化 intent_id 为 None
    intent_id = "None"

    # 检查文本是否包含任何中断关键词
    for keyword in processed_keywords:
        if keyword in text:
            intent_id = 'Y'
            break

    return {
        "intent_id": intent_id,
        "extracted_history": extracted_history,
        "extracted_msg": extracted_msg,
        "is_short_text": is_short_text
    }