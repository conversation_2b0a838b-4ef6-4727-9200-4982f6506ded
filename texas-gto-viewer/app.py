"""
德州扑克GTO查看器 - 主应用
"""

import streamlit as st
import plotly.graph_objects as go
import plotly.express as px
import pandas as pd
import numpy as np
from poker_utils import Card, Hand, Range, PositionRanges, HandEvaluator
from gto_calculator import GameState, SimplifiedCFR, DeviationAnalyzer

# 页面配置
st.set_page_config(
    page_title="德州扑克 GTO 查看器",
    page_icon="🃏",
    layout="wide",
    initial_sidebar_state="expanded"
)

# 自定义CSS
st.markdown("""
<style>
    .main {
        padding: 1rem;
    }
    .stButton>button {
        width: 100%;
        margin: 0.25rem 0;
    }
    .card {
        padding: 1.5rem;
        border-radius: 0.5rem;
        background-color: #f0f2f6;
        margin: 1rem 0;
    }
    .metric-card {
        background-color: white;
        padding: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        text-align: center;
    }
    .deviation-perfect { color: #28a745; font-weight: bold; }
    .deviation-good { color: #6c757d; }
    .deviation-ok { color: #ffc107; }
    .deviation-bad { color: #fd7e14; }
    .deviation-terrible { color: #dc3545; font-weight: bold; }
</style>
""", unsafe_allow_html=True)

# 初始化session state
if 'game_history' not in st.session_state:
    st.session_state.game_history = []
if 'current_hand' not in st.session_state:
    st.session_state.current_hand = None
if 'current_board' not in st.session_state:
    st.session_state.current_board = []
if 'action_history' not in st.session_state:
    st.session_state.action_history = []

def parse_card_input(card_str: str) -> Card:
    """解析卡牌输入"""
    if len(card_str) != 2:
        return None
    rank = card_str[0].upper()
    suit = card_str[1].lower()
    if rank not in Card.RANKS or suit not in Card.SUITS:
        return None
    return Card(rank, suit)

def create_range_heatmap(range_obj: Range):
    """创建范围热力图"""
    # 创建13x13矩阵
    ranks = list(Card.RANKS[::-1])  # 从A到2
    matrix = np.zeros((13, 13))
    labels = [['' for _ in range(13)] for _ in range(13)]
    
    # 填充矩阵
    for hand in range_obj.hands:
        c1, c2 = hand
        i1, i2 = ranks.index(c1.rank), ranks.index(c2.rank)
        
        if i1 == i2:  # 对子
            matrix[i1, i2] += range_obj.weights.get(hand, 1.0)
            labels[i1][i2] = f"{c1.rank}{c2.rank}"
        elif c1.suit == c2.suit:  # 同花
            matrix[min(i1, i2), max(i1, i2)] += range_obj.weights.get(hand, 1.0)
            labels[min(i1, i2)][max(i1, i2)] = f"{ranks[min(i1, i2)]}{ranks[max(i1, i2)]}s"
        else:  # 非同花
            matrix[max(i1, i2), min(i1, i2)] += range_obj.weights.get(hand, 1.0)
            labels[max(i1, i2)][min(i1, i2)] = f"{ranks[max(i1, i2)]}{ranks[min(i1, i2)]}o"
    
    # 归一化
    max_val = np.max(matrix)
    if max_val > 0:
        matrix = matrix / max_val
    
    # 创建热力图
    fig = go.Figure(data=go.Heatmap(
        z=matrix,
        text=labels,
        texttemplate="%{text}",
        textfont={"size": 10},
        colorscale="RdYlGn",
        showscale=False,
        hovertemplate="手牌: %{text}<br>权重: %{z:.2f}<extra></extra>"
    ))
    
    fig.update_layout(
        width=500,
        height=500,
        xaxis=dict(tickmode='array', tickvals=list(range(13)), ticktext=ranks),
        yaxis=dict(tickmode='array', tickvals=list(range(13)), ticktext=ranks),
        title="手牌范围矩阵"
    )
    
    return fig

def create_strategy_chart(gto_strategy: dict):
    """创建策略饼图"""
    actions = list(gto_strategy.keys())
    frequencies = list(gto_strategy.values())
    
    # 行动类型到颜色的映射
    color_map = {
        'fold': '#FF6B6B',
        'check': '#4ECDC4',
        'call': '#45B7D1',
        'bet': '#96CEB4',
        'raise': '#FECA57'
    }
    
    colors = []
    for action in actions:
        for key in color_map:
            if key in action:
                colors.append(color_map[key])
                break
        else:
            colors.append('#95A5A6')
    
    fig = go.Figure(data=[go.Pie(
        labels=actions,
        values=frequencies,
        hole=0.3,
        marker_colors=colors
    )])
    
    fig.update_traces(
        textposition='inside',
        textinfo='percent+label',
        hovertemplate='%{label}<br>频率: %{percent}<extra></extra>'
    )
    
    fig.update_layout(
        title="GTO策略分布",
        showlegend=False,
        height=400
    )
    
    return fig

def main():
    st.title("🃏 德州扑克 GTO 查看器")
    st.markdown("分析你的决策是否符合GTO（Game Theory Optimal）策略")
    
    # 侧边栏
    with st.sidebar:
        st.header("游戏设置")
        
        # 位置选择
        positions = ['UTG', 'MP', 'CO', 'BTN', 'SB', 'BB']
        hero_position = st.selectbox("你的位置", positions, index=3)
        villain_position = st.selectbox("对手位置", positions, index=5)
        
        # 筹码设置
        st.subheader("筹码量")
        hero_stack = st.number_input("你的筹码", min_value=10, max_value=1000, value=100, step=10)
        villain_stack = st.number_input("对手筹码", min_value=10, max_value=1000, value=100, step=10)
        
        # 底池
        pot_size = st.number_input("当前底池", min_value=0, max_value=500, value=10, step=5)
        
        st.divider()
        
        # 手牌历史
        st.subheader("手牌历史")
        if st.session_state.game_history:
            for i, record in enumerate(st.session_state.game_history[-5:]):
                st.text(f"{i+1}. {record['hand']} - {record['result']}")
        else:
            st.text("暂无历史记录")
    
    # 主要内容区域
    col1, col2 = st.columns([1, 1])
    
    with col1:
        st.header("输入手牌信息")
        
        # 手牌输入
        st.subheader("你的手牌")
        hand_input = st.text_input(
            "输入两张手牌 (例如: AhKs)", 
            placeholder="AhKs",
            help="格式：[牌面][花色][牌面][花色]，如 AhKs 表示红桃A和黑桃K"
        )
        
        # 公共牌输入
        st.subheader("公共牌")
        street = st.selectbox("当前街", ["翻前", "翻牌", "转牌", "河牌"])
        
        board_input = ""
        if street != "翻前":
            board_input = st.text_input(
                f"输入公共牌 (例如: {'QhJsTc' if street == '翻牌' else 'QhJsTc9d'})",
                placeholder="QhJsTc" if street == "翻牌" else "QhJsTc9d"
            )
        
        # 行动选择
        st.subheader("你的行动")
        
        # 根据情况显示不同的行动选项
        if not st.session_state.action_history:
            available_actions = ["过牌", "下注 0.5x底池", "下注 0.75x底池", "下注 1x底池"]
        else:
            last_action = st.session_state.action_history[-1]
            if "下注" in last_action or "加注" in last_action:
                available_actions = ["弃牌", "跟注", "加注 2x", "加注 3x"]
            else:
                available_actions = ["过牌", "下注 0.5x底池", "下注 0.75x底池", "下注 1x底池"]
        
        selected_action = st.selectbox("选择行动", available_actions)
        
        # 分析按钮
        if st.button("分析偏离度", type="primary"):
            # 解析输入
            if hand_input and len(hand_input) >= 4:
                try:
                    card1 = parse_card_input(hand_input[:2])
                    card2 = parse_card_input(hand_input[2:4])
                    
                    if card1 and card2:
                        hero_hand = [card1, card2]
                        
                        # 解析公共牌
                        board = []
                        if board_input:
                            for i in range(0, len(board_input), 2):
                                if i+1 < len(board_input):
                                    card = parse_card_input(board_input[i:i+2])
                                    if card:
                                        board.append(card)
                        
                        # 创建游戏状态
                        street_map = {"翻前": "preflop", "翻牌": "flop", "转牌": "turn", "河牌": "river"}
                        game_state = GameState(
                            pot=pot_size,
                            stack_hero=hero_stack,
                            stack_villain=villain_stack,
                            board=board,
                            street=street_map[street],
                            position=positions.index(hero_position) > positions.index(villain_position),
                            action_history=st.session_state.action_history.copy()
                        )
                        
                        # 获取范围
                        hero_range = Range()
                        hero_range.add_hand(Hand(hero_hand).to_string())
                        
                        villain_range = PositionRanges.get_opening_range(villain_position)
                        
                        # 运行GTO计算
                        with st.spinner("正在计算GTO策略..."):
                            cfr = SimplifiedCFR(iterations=100)  # 简化版本，快速计算
                            cfr.train(hero_range, villain_range, game_state)
                            
                            # 获取信息集
                            info_set = f"{hero_hand[0]}{hero_hand[1]}|{''.join(str(c) for c in board)}|{','.join(st.session_state.action_history)}"
                            
                            # 获取GTO策略
                            gto_strategy = cfr.strategy.get_strategy(info_set)
                            
                            # 映射用户行动到策略行动
                            action_map = {
                                "过牌": "check",
                                "弃牌": "fold",
                                "跟注": "call",
                                "下注 0.5x底池": "bet_0.5",
                                "下注 0.75x底池": "bet_0.75",
                                "下注 1x底池": "bet_1.0",
                                "加注 2x": "raise_2x",
                                "加注 3x": "raise_3x"
                            }
                            
                            actual_action = action_map.get(selected_action, "check")
                            
                            # 计算偏离度
                            deviation = DeviationAnalyzer.calculate_deviation(actual_action, gto_strategy)
                            
                            # 保存到session state
                            st.session_state.last_analysis = {
                                'hand': hand_input,
                                'board': board_input,
                                'action': selected_action,
                                'gto_strategy': gto_strategy,
                                'deviation': deviation,
                                'recommendation': DeviationAnalyzer.get_recommendation(gto_strategy, actual_action)
                            }
                            
                            # 添加到历史记录
                            st.session_state.game_history.append({
                                'hand': hand_input,
                                'result': deviation['severity']
                            })
                            
                            st.success("分析完成！")
                            
                except Exception as e:
                    st.error(f"输入错误: {str(e)}")
    
    with col2:
        st.header("分析结果")
        
        if 'last_analysis' in st.session_state:
            analysis = st.session_state.last_analysis
            
            # 偏离度指标
            col_metrics = st.columns(3)
            
            with col_metrics[0]:
                st.metric(
                    "偏离度评级",
                    analysis['deviation']['severity'],
                    delta=None
                )
            
            with col_metrics[1]:
                st.metric(
                    "EV损失",
                    f"{analysis['deviation']['ev_loss']:.3f}",
                    delta=f"-{analysis['deviation']['ev_loss_percentage']:.1f}%"
                )
            
            with col_metrics[2]:
                st.metric(
                    "GTO频率",
                    f"{analysis['deviation']['gto_frequency']:.1%}",
                    delta=None
                )
            
            # GTO策略分布
            st.subheader("GTO策略")
            fig_strategy = create_strategy_chart(analysis['gto_strategy'])
            st.plotly_chart(fig_strategy, use_container_width=True)
            
            # 建议
            st.subheader("改进建议")
            st.info(analysis['recommendation'])
            
            # 详细策略表格
            st.subheader("详细策略")
            strategy_df = pd.DataFrame(
                list(analysis['gto_strategy'].items()),
                columns=['行动', '频率']
            )
            strategy_df['频率'] = strategy_df['频率'].apply(lambda x: f"{x:.1%}")
            strategy_df = strategy_df.sort_values('频率', ascending=False)
            st.dataframe(strategy_df, use_container_width=True)
    
    # 范围分析标签页
    with st.expander("查看标准开局范围", expanded=False):
        st.subheader("不同位置的标准开局范围")
        
        selected_pos = st.selectbox("选择位置查看范围", positions)
        opening_range = PositionRanges.get_opening_range(selected_pos)
        
        if opening_range.hands:
            fig_range = create_range_heatmap(opening_range)
            st.plotly_chart(fig_range, use_container_width=True)
            
            # 显示具体范围字符串
            st.code(PositionRanges.RANGES_6MAX.get(selected_pos, "无数据"))
    
    # 底部信息
    st.divider()
    st.markdown("""
    ### 使用说明
    
    1. **输入手牌**: 使用标准记法，如 AhKs (红桃A黑桃K)
    2. **输入公共牌**: 翻牌输入3张，转牌4张，河牌5张
    3. **选择行动**: 选择你实际采取或想要分析的行动
    4. **查看结果**: 系统会显示你的行动与GTO策略的偏离度
    
    ### 偏离度等级
    
    - 🟢 **Perfect**: EV损失 < 1% - 完美的GTO打法
    - 🟢 **Good**: EV损失 < 5% - 良好的决策
    - 🟡 **OK**: EV损失 < 10% - 可接受的偏差
    - 🟠 **Bad**: EV损失 < 20% - 需要改进
    - 🔴 **Terrible**: EV损失 ≥ 20% - 严重偏离GTO
    
    *注：本工具使用简化的GTO计算，仅供学习参考*
    """)

if __name__ == "__main__":
    main() 