"""
德州扑克工具类
包含手牌评估、范围计算、牌型判断等功能
"""

import itertools
import numpy as np
from typing import List, Tuple, Dict, Set
from collections import defaultdict

class Card:
    """扑克牌类"""
    RANKS = '23456789TJQKA'
    SUITS = 'cdhs'  # clubs, diamonds, hearts, spades
    
    def __init__(self, rank: str, suit: str):
        self.rank = rank.upper()
        self.suit = suit.lower()
        self.rank_value = self.RANKS.index(self.rank)
        
    def __str__(self):
        return f"{self.rank}{self.suit}"
    
    def __repr__(self):
        return self.__str__()
    
    def __eq__(self, other):
        return self.rank == other.rank and self.suit == other.suit
    
    def __hash__(self):
        return hash((self.rank, self.suit))

class Hand:
    """手牌类"""
    def __init__(self, cards: List[Card]):
        self.cards = sorted(cards, key=lambda x: x.rank_value, reverse=True)
    
    def __str__(self):
        return ' '.join(str(card) for card in self.cards)
    
    def to_string(self):
        """转换为标准格式 (如 AKs, AKo)"""
        if len(self.cards) != 2:
            return str(self)
        
        c1, c2 = self.cards
        suited = 's' if c1.suit == c2.suit else 'o'
        if c1.rank == c2.rank:
            return f"{c1.rank}{c2.rank}"
        return f"{c1.rank}{c2.rank}{suited}"

class HandEvaluator:
    """手牌评估器"""
    
    @staticmethod
    def evaluate_hand(cards: List[Card]) -> Tuple[int, List[int]]:
        """
        评估手牌强度
        返回：(牌型等级, 关键牌值列表)
        """
        if len(cards) < 5:
            return (0, [])
        
        # 按点数分组
        rank_groups = defaultdict(list)
        for card in cards:
            rank_groups[card.rank_value].append(card)
        
        # 按花色分组
        suit_groups = defaultdict(list)
        for card in cards:
            suit_groups[card.suit].append(card)
        
        # 检查同花
        flush_suit = None
        for suit, suited_cards in suit_groups.items():
            if len(suited_cards) >= 5:
                flush_suit = suit
                break
        
        # 检查顺子
        rank_set = set(card.rank_value for card in cards)
        straight_high = None
        
        # 检查普通顺子
        for high in range(12, 3, -1):  # A到5
            if all(r in rank_set for r in range(high-4, high+1)):
                straight_high = high
                break
        
        # 检查A2345顺子
        if not straight_high and all(r in rank_set for r in [0, 1, 2, 3, 12]):
            straight_high = 3  # 5高顺子
        
        # 检查同花顺
        if flush_suit and straight_high:
            flush_cards = [c for c in cards if c.suit == flush_suit]
            flush_ranks = set(c.rank_value for c in flush_cards)
            
            # 检查同花顺
            for high in range(12, 3, -1):
                if all(r in flush_ranks for r in range(high-4, high+1)):
                    if high == 12:  # 皇家同花顺
                        return (10, [high])
                    return (9, [high])  # 同花顺
            
            # A2345同花顺
            if all(r in flush_ranks for r in [0, 1, 2, 3, 12]):
                return (9, [3])
        
        # 统计相同点数的牌
        counts = sorted([(len(cards), rank) for rank, cards in rank_groups.items()], 
                       reverse=True)
        
        # 四条
        if counts[0][0] == 4:
            return (8, [counts[0][1], counts[1][1]])
        
        # 葫芦
        if counts[0][0] == 3 and counts[1][0] >= 2:
            return (7, [counts[0][1], counts[1][1]])
        
        # 同花
        if flush_suit:
            flush_cards = sorted([c for c in cards if c.suit == flush_suit], 
                                key=lambda x: x.rank_value, reverse=True)
            return (6, [c.rank_value for c in flush_cards[:5]])
        
        # 顺子
        if straight_high is not None:
            return (5, [straight_high])
        
        # 三条
        if counts[0][0] == 3:
            kickers = [counts[i][1] for i in range(1, min(3, len(counts)))]
            return (4, [counts[0][1]] + kickers)
        
        # 两对
        if counts[0][0] == 2 and counts[1][0] == 2:
            kicker = counts[2][1] if len(counts) > 2 else 0
            return (3, [counts[0][1], counts[1][1], kicker])
        
        # 一对
        if counts[0][0] == 2:
            kickers = [counts[i][1] for i in range(1, min(4, len(counts)))]
            return (2, [counts[0][1]] + kickers)
        
        # 高牌
        high_cards = sorted([c.rank_value for c in cards], reverse=True)[:5]
        return (1, high_cards)

class Range:
    """手牌范围类"""
    
    def __init__(self):
        self.hands = set()
        self.weights = {}  # 每手牌的权重
        
    def add_hand(self, hand_str: str, weight: float = 1.0):
        """添加手牌到范围"""
        # 解析手牌字符串，如 "AKs", "AKo", "AA"
        if len(hand_str) == 2:  # 对子
            rank = hand_str[0]
            for s1, s2 in itertools.combinations(Card.SUITS, 2):
                hand = (Card(rank, s1), Card(rank, s2))
                self.hands.add(hand)
                self.weights[hand] = weight
        elif len(hand_str) == 3:
            r1, r2, suited = hand_str[0], hand_str[1], hand_str[2]
            if suited == 's':  # 同花
                for suit in Card.SUITS:
                    hand = (Card(r1, suit), Card(r2, suit))
                    self.hands.add(hand)
                    self.weights[hand] = weight
            else:  # 非同花
                for s1, s2 in itertools.product(Card.SUITS, repeat=2):
                    if s1 != s2:
                        hand = (Card(r1, s1), Card(r2, s2))
                        self.hands.add(hand)
                        self.weights[hand] = weight
    
    def add_range_string(self, range_str: str):
        """从范围字符串添加手牌，如 "AA-TT,AKs-ATs,AKo-AJo" """
        parts = range_str.replace(' ', '').split(',')
        for part in parts:
            if '-' in part:
                start, end = part.split('-')
                self._add_range(start, end)
            else:
                self.add_hand(part)
    
    def _add_range(self, start: str, end: str):
        """添加范围，如 AA-TT"""
        if len(start) == 2 and start[0] == start[1]:  # 对子范围
            start_rank = Card.RANKS.index(start[0])
            end_rank = Card.RANKS.index(end[0])
            for i in range(start_rank, end_rank - 1, -1):
                rank = Card.RANKS[i]
                self.add_hand(f"{rank}{rank}")
        elif len(start) == 3:  # 非对子范围
            suited = start[2]
            start_high = Card.RANKS.index(start[0])
            start_low = Card.RANKS.index(start[1])
            end_low = Card.RANKS.index(end[1])
            
            for low in range(start_low, end_low - 1, -1):
                hand_str = f"{start[0]}{Card.RANKS[low]}{suited}"
                self.add_hand(hand_str)
    
    def get_equity_vs_range(self, hero_hand: List[Card], board: List[Card], 
                           villain_range: 'Range', iterations: int = 1000) -> float:
        """计算对抗范围的胜率"""
        wins = 0
        total = 0
        
        # 简化计算：随机抽样
        for _ in range(iterations):
            # 从对手范围随机选一手牌
            villain_hand = self._sample_hand(villain_range)
            if not villain_hand:
                continue
                
            # 确保没有重复牌
            all_cards = set(hero_hand + board + list(villain_hand))
            if len(all_cards) != len(hero_hand) + len(board) + 2:
                continue
            
            # 模拟剩余公共牌
            deck = self._get_remaining_deck(all_cards)
            remaining_cards = 5 - len(board)
            
            if remaining_cards > 0:
                community = board + list(np.random.choice(deck, remaining_cards, replace=False))
            else:
                community = board
            
            # 比较牌力
            hero_strength = HandEvaluator.evaluate_hand(hero_hand + community)
            villain_strength = HandEvaluator.evaluate_hand(list(villain_hand) + community)
            
            if self._compare_hands(hero_strength, villain_strength) > 0:
                wins += 1
            elif self._compare_hands(hero_strength, villain_strength) == 0:
                wins += 0.5
            
            total += 1
        
        return wins / total if total > 0 else 0.5
    
    def _sample_hand(self, range_obj: 'Range') -> Tuple[Card, Card]:
        """从范围中随机采样一手牌"""
        if not range_obj.hands:
            return None
        
        # 根据权重采样
        hands = list(range_obj.hands)
        weights = [range_obj.weights.get(h, 1.0) for h in hands]
        total_weight = sum(weights)
        weights = [w/total_weight for w in weights]
        
        return hands[np.random.choice(len(hands), p=weights)]
    
    def _get_remaining_deck(self, used_cards: Set[Card]) -> List[Card]:
        """获取剩余牌组"""
        deck = []
        for rank in Card.RANKS:
            for suit in Card.SUITS:
                card = Card(rank, suit)
                if card not in used_cards:
                    deck.append(card)
        return deck
    
    def _compare_hands(self, hand1: Tuple[int, List[int]], 
                      hand2: Tuple[int, List[int]]) -> int:
        """比较两手牌，返回1(hand1赢)，0(平局)，-1(hand2赢)"""
        if hand1[0] > hand2[0]:
            return 1
        elif hand1[0] < hand2[0]:
            return -1
        else:
            # 比较关键牌
            for v1, v2 in zip(hand1[1], hand2[1]):
                if v1 > v2:
                    return 1
                elif v1 < v2:
                    return -1
            return 0

class PositionRanges:
    """不同位置的标准范围"""
    
    # 6人桌标准开局范围
    RANGES_6MAX = {
        'UTG': 'AA-77,AKs-ATs,KQs-KTs,QJs-QTs,JTs,T9s,98s,87s,76s,AKo-AJo,KQo',
        'MP': 'AA-66,AKs-A9s,KQs-KTs,QJs-QTs,JTs-J9s,T9s,98s,87s,76s,65s,AKo-ATo,KQo-KJo',
        'CO': 'AA-44,AKs-A2s,KQs-K9s,QJs-Q9s,JTs-J9s,T9s-T8s,98s-97s,87s-86s,76s-75s,65s,54s,AKo-A9o,KQo-KTo,QJo',
        'BTN': 'AA-22,AKs-A2s,KQs-K2s,QJs-Q8s,JTs-J8s,T9s-T7s,98s-96s,87s-85s,76s-74s,65s-64s,54s-53s,43s,AKo-A2o,KQo-K9o,QJo-Q9o,JTo-J9o,T9o',
        'SB': 'AA-44,AKs-A8s,KQs-KTs,QJs-QTs,JTs,T9s,98s,87s,AKo-ATo,KQo',
        'BB': 'AA-22,AKs-A2s,KQs-K2s,QJs-Q2s,JTs-J4s,T9s-T6s,98s-95s,87s-84s,76s-73s,65s-63s,54s-52s,43s,32s,AKo-A2o,KQo-K8o,QJo-Q8o,JTo-J8o,T9o-T8o,98o'
    }
    
    # 3-bet范围
    THREBET_RANGES = {
        'UTG_vs_MP': 'AA-QQ,AKs,AKo',
        'MP_vs_CO': 'AA-QQ,AKs,AKo',
        'CO_vs_BTN': 'AA-JJ,AKs-AQs,AKo',
        'BTN_vs_SB': 'AA-99,AKs-AJs,KQs,AKo-AQo',
        'SB_vs_BB': 'AA-TT,AKs-AQs,AKo'
    }
    
    @classmethod
    def get_opening_range(cls, position: str, table_size: int = 6) -> Range:
        """获取特定位置的开局范围"""
        range_obj = Range()
        if table_size == 6 and position in cls.RANGES_6MAX:
            range_obj.add_range_string(cls.RANGES_6MAX[position])
        return range_obj
    
    @classmethod
    def get_3bet_range(cls, position: str, villain_position: str) -> Range:
        """获取3-bet范围"""
        range_obj = Range()
        key = f"{position}_vs_{villain_position}"
        if key in cls.THREBET_RANGES:
            range_obj.add_range_string(cls.THREBET_RANGES[key])
        return range_obj 