"""
GTO策略计算器
使用简化的CFR (Counterfactual Regret Minimization) 算法
"""

import numpy as np
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
from poker_utils import Card, Hand, Range, HandEvaluator
import random

@dataclass
class GameState:
    """游戏状态"""
    pot: float
    stack_hero: float
    stack_villain: float
    board: List[Card]
    street: str  # 'preflop', 'flop', 'turn', 'river'
    position: bool  # True if hero has position
    action_history: List[str]
    
@dataclass
class Action:
    """行动"""
    type: str  # 'fold', 'check', 'call', 'bet', 'raise'
    amount: float = 0
    
    def __str__(self):
        if self.type in ['fold', 'check', 'call']:
            return self.type
        return f"{self.type} {self.amount}"

class GTOStrategy:
    """GTO策略"""
    def __init__(self):
        self.strategies = {}  # 存储各种情况下的策略
        self.regrets = {}     # 存储遗憾值
        
    def get_strategy(self, info_set: str) -> Dict[str, float]:
        """获取特定信息集的策略"""
        if info_set not in self.strategies:
            # 初始化为均匀策略
            actions = self._get_available_actions(info_set)
            self.strategies[info_set] = {a: 1.0 / len(actions) for a in actions}
        return self.strategies[info_set]
    
    def _get_available_actions(self, info_set: str) -> List[str]:
        """根据信息集获取可用行动"""
        # 简化版本：返回基本行动
        if 'check' in info_set:
            return ['check', 'bet_0.5', 'bet_0.75', 'bet_1.0']
        elif 'bet' in info_set or 'raise' in info_set:
            return ['fold', 'call', 'raise_2x', 'raise_3x']
        else:
            return ['fold', 'check', 'bet_0.5', 'bet_0.75']

class SimplifiedCFR:
    """简化的CFR算法实现"""
    
    def __init__(self, iterations: int = 1000):
        self.iterations = iterations
        self.strategy = GTOStrategy()
        self.cumulative_regrets = {}
        self.cumulative_strategy = {}
        
    def train(self, hero_range: Range, villain_range: Range, game_state: GameState):
        """训练GTO策略"""
        for _ in range(self.iterations):
            # 随机选择手牌
            hero_hand = self._sample_hand(hero_range)
            villain_hand = self._sample_hand(villain_range)
            
            # 确保没有重复牌
            if self._has_card_conflicts(hero_hand, villain_hand, game_state.board):
                continue
                
            # 运行CFR迭代
            self._cfr(game_state, hero_hand, villain_hand, 1.0, 1.0)
            
        # 计算平均策略
        self._compute_average_strategy()
    
    def _sample_hand(self, range_obj: Range) -> Tuple[Card, Card]:
        """从范围中采样手牌"""
        hands = list(range_obj.hands)
        return random.choice(hands) if hands else None
    
    def _has_card_conflicts(self, hand1, hand2, board):
        """检查是否有重复牌"""
        if not hand1 or not hand2:
            return True
        all_cards = set(list(hand1) + list(hand2) + board)
        return len(all_cards) != len(hand1) + len(hand2) + len(board)
    
    def _cfr(self, state: GameState, hero_hand, villain_hand, p0, p1):
        """CFR递归函数"""
        # 检查终止条件
        if self._is_terminal(state):
            return self._get_payoff(state, hero_hand, villain_hand)
        
        # 获取当前玩家
        player = len(state.action_history) % 2
        
        # 获取信息集
        info_set = self._get_info_set(state, hero_hand if player == 0 else villain_hand)
        
        # 获取策略
        strategy = self._get_strategy(info_set)
        actions = list(strategy.keys())
        
        # 计算每个行动的价值
        action_values = {}
        node_value = 0
        
        for action in actions:
            # 创建新状态
            new_state = self._apply_action(state, action)
            
            # 递归计算价值
            if player == 0:
                action_values[action] = self._cfr(new_state, hero_hand, villain_hand, 
                                                 p0 * strategy[action], p1)
            else:
                action_values[action] = -self._cfr(new_state, hero_hand, villain_hand, 
                                                  p0, p1 * strategy[action])
            
            node_value += strategy[action] * action_values[action]
        
        # 更新遗憾值
        if player == 0:
            for action in actions:
                regret = action_values[action] - node_value
                self._update_regret(info_set, action, p1 * regret)
            
            # 更新策略
            self._update_strategy(info_set, p0)
        
        return node_value
    
    def _is_terminal(self, state: GameState) -> bool:
        """检查是否是终止状态"""
        # 简化版本
        if 'fold' in state.action_history:
            return True
        if state.street == 'river' and state.action_history.count('check') >= 2:
            return True
        if state.street == 'river' and 'call' in state.action_history[-1:]:
            return True
        return False
    
    def _get_payoff(self, state: GameState, hero_hand, villain_hand) -> float:
        """计算收益"""
        if 'fold' in state.action_history:
            # 某人弃牌
            last_actor = (len(state.action_history) - 1) % 2
            if state.action_history[-1] == 'fold':
                return state.pot if last_actor == 1 else -state.pot
        
        # 摊牌
        hero_strength = HandEvaluator.evaluate_hand(list(hero_hand) + state.board)
        villain_strength = HandEvaluator.evaluate_hand(list(villain_hand) + state.board)
        
        if self._compare_hands(hero_strength, villain_strength) > 0:
            return state.pot
        elif self._compare_hands(hero_strength, villain_strength) < 0:
            return -state.pot
        else:
            return 0
    
    def _compare_hands(self, hand1, hand2):
        """比较手牌强度"""
        if hand1[0] > hand2[0]:
            return 1
        elif hand1[0] < hand2[0]:
            return -1
        else:
            for v1, v2 in zip(hand1[1], hand2[1]):
                if v1 > v2:
                    return 1
                elif v1 < v2:
                    return -1
            return 0
    
    def _get_info_set(self, state: GameState, hand) -> str:
        """获取信息集字符串"""
        # 简化版本：手牌+公共牌+行动历史
        hand_str = f"{hand[0]}{hand[1]}"
        board_str = ''.join(str(card) for card in state.board)
        action_str = ','.join(state.action_history)
        return f"{hand_str}|{board_str}|{action_str}"
    
    def _get_strategy(self, info_set: str) -> Dict[str, float]:
        """获取策略"""
        if info_set not in self.cumulative_regrets:
            # 初始化
            actions = self._get_available_actions_from_info_set(info_set)
            self.cumulative_regrets[info_set] = {a: 0 for a in actions}
            self.cumulative_strategy[info_set] = {a: 0 for a in actions}
        
        # 根据遗憾值计算策略
        regrets = self.cumulative_regrets[info_set]
        positive_regrets = {a: max(0, r) for a, r in regrets.items()}
        sum_positive = sum(positive_regrets.values())
        
        if sum_positive > 0:
            strategy = {a: r / sum_positive for a, r in positive_regrets.items()}
        else:
            # 均匀策略
            actions = list(regrets.keys())
            strategy = {a: 1.0 / len(actions) for a in actions}
        
        return strategy
    
    def _get_available_actions_from_info_set(self, info_set: str) -> List[str]:
        """从信息集获取可用行动"""
        # 简化版本
        action_history = info_set.split('|')[-1]
        if not action_history or action_history == '':
            return ['check', 'bet_0.5', 'bet_0.75', 'bet_1.0']
        elif 'bet' in action_history or 'raise' in action_history:
            return ['fold', 'call', 'raise_2x']
        else:
            return ['check', 'bet_0.5', 'bet_0.75']
    
    def _apply_action(self, state: GameState, action: str) -> GameState:
        """应用行动到游戏状态"""
        # 创建新状态
        new_state = GameState(
            pot=state.pot,
            stack_hero=state.stack_hero,
            stack_villain=state.stack_villain,
            board=state.board.copy(),
            street=state.street,
            position=state.position,
            action_history=state.action_history + [action]
        )
        
        # 更新底池和筹码
        if 'bet' in action:
            bet_size = float(action.split('_')[1]) * state.pot
            if len(state.action_history) % 2 == 0:
                new_state.stack_hero -= bet_size
            else:
                new_state.stack_villain -= bet_size
            new_state.pot += bet_size
        elif action == 'call':
            # 简化：跟注前一个下注
            call_amount = state.pot * 0.5  # 简化假设
            if len(state.action_history) % 2 == 0:
                new_state.stack_hero -= call_amount
            else:
                new_state.stack_villain -= call_amount
            new_state.pot += call_amount
        
        return new_state
    
    def _update_regret(self, info_set: str, action: str, regret: float):
        """更新遗憾值"""
        if info_set not in self.cumulative_regrets:
            self.cumulative_regrets[info_set] = {}
        if action not in self.cumulative_regrets[info_set]:
            self.cumulative_regrets[info_set][action] = 0
        self.cumulative_regrets[info_set][action] += regret
    
    def _update_strategy(self, info_set: str, reach_probability: float):
        """更新策略"""
        strategy = self._get_strategy(info_set)
        if info_set not in self.cumulative_strategy:
            self.cumulative_strategy[info_set] = {}
        
        for action, prob in strategy.items():
            if action not in self.cumulative_strategy[info_set]:
                self.cumulative_strategy[info_set][action] = 0
            self.cumulative_strategy[info_set][action] += reach_probability * prob
    
    def _compute_average_strategy(self):
        """计算平均策略"""
        self.strategy.strategies = {}
        
        for info_set, action_sums in self.cumulative_strategy.items():
            total = sum(action_sums.values())
            if total > 0:
                self.strategy.strategies[info_set] = {
                    a: s / total for a, s in action_sums.items()
                }
            else:
                actions = list(action_sums.keys())
                self.strategy.strategies[info_set] = {
                    a: 1.0 / len(actions) for a in actions
                }

class DeviationAnalyzer:
    """偏离度分析器"""
    
    @staticmethod
    def calculate_deviation(actual_action: str, gto_strategy: Dict[str, float]) -> Dict[str, float]:
        """计算实际行动与GTO策略的偏离度"""
        deviation = {}
        
        # 计算EV损失
        gto_ev = max(gto_strategy.values())
        actual_ev = gto_strategy.get(actual_action, 0)
        ev_loss = gto_ev - actual_ev
        
        deviation['ev_loss'] = ev_loss
        deviation['ev_loss_percentage'] = (ev_loss / gto_ev * 100) if gto_ev > 0 else 0
        
        # 计算频率偏差
        deviation['gto_frequency'] = gto_strategy.get(actual_action, 0)
        deviation['frequency_deviation'] = 1.0 - deviation['gto_frequency']
        
        # 分类偏离程度
        if ev_loss < 0.01:
            deviation['severity'] = 'Perfect'
            deviation['color'] = 'green'
        elif ev_loss < 0.05:
            deviation['severity'] = 'Good'
            deviation['color'] = 'lightgreen'
        elif ev_loss < 0.1:
            deviation['severity'] = 'OK'
            deviation['color'] = 'yellow'
        elif ev_loss < 0.2:
            deviation['severity'] = 'Bad'
            deviation['color'] = 'orange'
        else:
            deviation['severity'] = 'Terrible'
            deviation['color'] = 'red'
        
        return deviation
    
    @staticmethod
    def get_recommendation(gto_strategy: Dict[str, float], actual_action: str) -> str:
        """获取改进建议"""
        sorted_actions = sorted(gto_strategy.items(), key=lambda x: x[1], reverse=True)
        best_action = sorted_actions[0][0]
        
        if actual_action == best_action:
            return "完美！你的行动符合GTO策略。"
        
        recommendations = []
        recommendations.append(f"GTO建议: {best_action} ({gto_strategy[best_action]:.1%})")
        
        # 添加混合策略建议
        mixed_actions = [f"{action} ({freq:.1%})" 
                        for action, freq in sorted_actions[:3] 
                        if freq > 0.1]
        
        if len(mixed_actions) > 1:
            recommendations.append(f"混合策略: {', '.join(mixed_actions)}")
        
        # 解释为什么某个行动更好
        if 'fold' in best_action and actual_action != 'fold':
            recommendations.append("考虑弃牌 - 你的手牌在这种情况下期望值为负")
        elif 'raise' in best_action and 'call' in actual_action:
            recommendations.append("考虑加注 - 施加压力并建立底池")
        elif 'check' in best_action and 'bet' in actual_action:
            recommendations.append("考虑过牌 - 控制底池大小并引诱对手")
        
        return '\n'.join(recommendations) 