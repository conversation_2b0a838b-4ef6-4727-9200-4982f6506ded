#!/bin/bash

# 德州扑克GTO查看器启动脚本

echo "🃏 正在启动德州扑克GTO查看器..."
echo ""

# 检查Python环境
if ! command -v python3 &> /dev/null; then
    echo "❌ 错误：未找到Python3，请先安装Python"
    exit 1
fi

# 检查依赖
if ! python3 -c "import streamlit" &> /dev/null; then
    echo "📦 正在安装依赖..."
    pip install --trusted-host pypi.org --trusted-host files.pythonhosted.org -r requirements.txt
fi

# 启动应用
echo "✅ 启动应用..."
echo "🌐 应用将在浏览器中打开，地址：http://localhost:8501"
echo ""
echo "按 Ctrl+C 停止应用"
echo ""

streamlit run app.py 