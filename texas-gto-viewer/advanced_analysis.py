"""
高级分析模块
包含胜率计算、范围对比、历史手牌分析等功能
"""

import numpy as np
import pandas as pd
from typing import List, Dict, Tuple
from poker_utils import Card, Hand, Range, HandEvaluator
import itertools
from collections import defaultdict

class EquityCalculator:
    """胜率计算器"""
    
    @staticmethod
    def calculate_equity(hero_cards: List[Card], villain_range: Range, 
                        board: List[Card] = [], iterations: int = 10000) -> Dict[str, float]:
        """计算对抗范围的胜率"""
        wins = 0
        ties = 0
        total = 0
        
        # 创建剩余牌组
        used_cards = set(hero_cards + board)
        deck = []
        for rank in Card.RANKS:
            for suit in Card.SUITS:
                card = Card(rank, suit)
                if card not in used_cards:
                    deck.append(card)
        
        # 蒙特卡洛模拟
        for _ in range(iterations):
            # 随机选择对手手牌
            villain_hand = None
            for _ in range(100):  # 最多尝试100次
                potential_hand = np.random.choice(list(villain_range.hands))
                if not any(card in used_cards for card in potential_hand):
                    villain_hand = potential_hand
                    break
            
            if not villain_hand:
                continue
            
            # 创建临时使用牌集合
            temp_used = used_cards | set(villain_hand)
            available_deck = [c for c in deck if c not in temp_used]
            
            # 随机完成公共牌
            remaining = 5 - len(board)
            if remaining > 0 and len(available_deck) >= remaining:
                community = board + list(np.random.choice(available_deck, remaining, replace=False))
            else:
                community = board
            
            # 评估手牌
            hero_strength = HandEvaluator.evaluate_hand(hero_cards + community)
            villain_strength = HandEvaluator.evaluate_hand(list(villain_hand) + community)
            
            # 比较结果
            if hero_strength[0] > villain_strength[0]:
                wins += 1
            elif hero_strength[0] == villain_strength[0]:
                # 比较高牌
                tie = True
                for h, v in zip(hero_strength[1], villain_strength[1]):
                    if h > v:
                        wins += 1
                        tie = False
                        break
                    elif h < v:
                        tie = False
                        break
                if tie:
                    ties += 1
            
            total += 1
        
        if total == 0:
            return {'win': 0.5, 'tie': 0, 'lose': 0.5}
        
        win_rate = wins / total
        tie_rate = ties / total
        lose_rate = 1 - win_rate - tie_rate
        
        return {
            'win': win_rate,
            'tie': tie_rate,
            'lose': lose_rate,
            'equity': win_rate + tie_rate / 2
        }

class RangeAnalyzer:
    """范围分析器"""
    
    @staticmethod
    def compare_ranges(range1: Range, range2: Range) -> Dict[str, any]:
        """比较两个范围"""
        # 计算范围大小
        size1 = len(range1.hands)
        size2 = len(range2.hands)
        
        # 找出共同手牌
        common_hands = set()
        for h1 in range1.hands:
            for h2 in range2.hands:
                if h1[0].rank == h2[0].rank and h1[1].rank == h2[1].rank:
                    if (h1[0].suit == h1[1].suit) == (h2[0].suit == h2[1].suit):
                        common_hands.add((h1[0].rank, h1[1].rank, 's' if h1[0].suit == h1[1].suit else 'o'))
        
        # 计算范围强度分布
        strength1 = RangeAnalyzer._calculate_range_strength(range1)
        strength2 = RangeAnalyzer._calculate_range_strength(range2)
        
        return {
            'size_ratio': size1 / size2 if size2 > 0 else float('inf'),
            'range1_size': size1,
            'range2_size': size2,
            'common_hands': len(common_hands),
            'overlap_percentage': len(common_hands) / min(size1, size2) * 100 if min(size1, size2) > 0 else 0,
            'strength_comparison': {
                'range1_avg': strength1['average'],
                'range2_avg': strength2['average'],
                'difference': strength1['average'] - strength2['average']
            }
        }
    
    @staticmethod
    def _calculate_range_strength(range_obj: Range) -> Dict[str, float]:
        """计算范围的平均强度"""
        if not range_obj.hands:
            return {'average': 0, 'premium': 0, 'strong': 0, 'medium': 0, 'weak': 0}
        
        premium_hands = ['AA', 'KK', 'QQ', 'AKs', 'AKo']
        strong_hands = ['JJ', 'TT', '99', 'AQs', 'AQo', 'KQs']
        
        premium_count = 0
        strong_count = 0
        total_strength = 0
        
        for hand in range_obj.hands:
            hand_str = Hand([hand[0], hand[1]]).to_string()
            
            # 简化的手牌强度评分
            if hand_str in premium_hands:
                premium_count += 1
                total_strength += 10
            elif hand_str in strong_hands:
                strong_count += 1
                total_strength += 8
            elif hand[0].rank == hand[1].rank:  # 对子
                total_strength += 6
            elif hand[0].suit == hand[1].suit:  # 同花
                total_strength += 5
            else:
                total_strength += 3
        
        avg_strength = total_strength / len(range_obj.hands)
        
        return {
            'average': avg_strength,
            'premium': premium_count / len(range_obj.hands) * 100,
            'strong': strong_count / len(range_obj.hands) * 100,
            'medium': (len(range_obj.hands) - premium_count - strong_count) / len(range_obj.hands) * 50,
            'weak': (len(range_obj.hands) - premium_count - strong_count) / len(range_obj.hands) * 50
        }

class HandHistoryAnalyzer:
    """手牌历史分析器"""
    
    def __init__(self):
        self.history = []
    
    def add_hand(self, hand_data: Dict):
        """添加手牌记录"""
        self.history.append({
            'timestamp': pd.Timestamp.now(),
            **hand_data
        })
    
    def get_statistics(self) -> Dict[str, any]:
        """获取统计数据"""
        if not self.history:
            return {}
        
        df = pd.DataFrame(self.history)
        
        # 按偏离度统计
        deviation_stats = df['deviation_severity'].value_counts().to_dict()
        
        # 按位置统计
        position_stats = df.groupby('position')['ev_loss'].mean().to_dict()
        
        # 按行动类型统计
        action_stats = df.groupby('action_type')['ev_loss'].mean().to_dict()
        
        # 计算趋势
        df['timestamp'] = pd.to_datetime(df['timestamp'])
        df = df.sort_values('timestamp')
        df['cumulative_ev_loss'] = df['ev_loss'].cumsum()
        
        return {
            'total_hands': len(self.history),
            'average_ev_loss': df['ev_loss'].mean(),
            'deviation_distribution': deviation_stats,
            'position_performance': position_stats,
            'action_performance': action_stats,
            'improvement_trend': self._calculate_improvement_trend(df)
        }
    
    def _calculate_improvement_trend(self, df: pd.DataFrame) -> str:
        """计算改进趋势"""
        if len(df) < 10:
            return "数据不足"
        
        # 比较最近10手和之前的平均EV损失
        recent = df.tail(10)['ev_loss'].mean()
        previous = df.iloc[:-10]['ev_loss'].mean()
        
        if recent < previous * 0.8:
            return "显著改善"
        elif recent < previous * 0.95:
            return "略有改善"
        elif recent > previous * 1.2:
            return "表现下滑"
        else:
            return "保持稳定"

class BettingSizeOptimizer:
    """下注尺度优化器"""
    
    @staticmethod
    def get_optimal_bet_sizes(pot: float, stack: float, street: str) -> List[Dict[str, float]]:
        """获取最优下注尺度"""
        spr = stack / pot  # Stack-to-Pot Ratio
        
        sizes = []
        
        # 根据SPR和街道调整下注尺度
        if street == 'flop':
            if spr > 10:
                sizes.extend([
                    {'size': 0.33, 'frequency': 0.3, 'purpose': '薄价值/诈唬'},
                    {'size': 0.5, 'frequency': 0.4, 'purpose': '标准持续下注'},
                    {'size': 0.75, 'frequency': 0.2, 'purpose': '强价值/强诈唬'},
                    {'size': 1.0, 'frequency': 0.1, 'purpose': '两极化'}
                ])
            elif spr > 5:
                sizes.extend([
                    {'size': 0.5, 'frequency': 0.5, 'purpose': '标准'},
                    {'size': 0.75, 'frequency': 0.35, 'purpose': '价值'},
                    {'size': 1.0, 'frequency': 0.15, 'purpose': '极化'}
                ])
            else:
                sizes.extend([
                    {'size': 0.75, 'frequency': 0.6, 'purpose': '标准'},
                    {'size': 1.0, 'frequency': 0.3, 'purpose': '价值'},
                    {'size': 'all-in', 'frequency': 0.1, 'purpose': '全下'}
                ])
        
        elif street == 'turn':
            if spr > 5:
                sizes.extend([
                    {'size': 0.5, 'frequency': 0.25, 'purpose': '阻隔/薄价值'},
                    {'size': 0.75, 'frequency': 0.5, 'purpose': '标准'},
                    {'size': 1.0, 'frequency': 0.25, 'purpose': '极化'}
                ])
            else:
                sizes.extend([
                    {'size': 0.75, 'frequency': 0.4, 'purpose': '标准'},
                    {'size': 1.0, 'frequency': 0.4, 'purpose': '价值'},
                    {'size': 'all-in', 'frequency': 0.2, 'purpose': '全下'}
                ])
        
        elif street == 'river':
            if spr > 2:
                sizes.extend([
                    {'size': 0.33, 'frequency': 0.2, 'purpose': '阻隔/薄价值'},
                    {'size': 0.5, 'frequency': 0.3, 'purpose': '价值'},
                    {'size': 0.75, 'frequency': 0.3, 'purpose': '强价值'},
                    {'size': 1.0, 'frequency': 0.2, 'purpose': '极化'}
                ])
            else:
                sizes.extend([
                    {'size': 0.5, 'frequency': 0.3, 'purpose': '小价值'},
                    {'size': 'all-in', 'frequency': 0.7, 'purpose': '全下'}
                ])
        
        return sizes

class ImpliedOddsCalculator:
    """隐含赔率计算器"""
    
    @staticmethod
    def calculate_implied_odds(pot: float, bet: float, hero_stack: float, 
                              villain_stack: float, outs: int, cards_to_come: int) -> Dict[str, float]:
        """计算隐含赔率"""
        # 计算成牌概率
        hit_probability = 1 - ((47 - outs) / 47) ** cards_to_come
        
        # 计算需要的底池赔率
        pot_odds = bet / (pot + bet)
        
        # 计算需要赢取的额外筹码
        needed_win = (bet / hit_probability) - pot - bet
        
        # 考虑有效筹码
        effective_stack = min(hero_stack - bet, villain_stack)
        
        # 计算实际可能赢取的筹码
        realistic_win = min(needed_win, effective_stack * 0.5)  # 假设平均能赢取一半有效筹码
        
        # 计算实际隐含赔率
        actual_implied_odds = (pot + realistic_win) / bet
        
        return {
            'hit_probability': hit_probability * 100,
            'pot_odds_needed': pot_odds * 100,
            'additional_needed': max(0, needed_win),
            'realistic_win': realistic_win,
            'implied_odds': actual_implied_odds,
            'call_profitable': actual_implied_odds > (1 / hit_probability),
            'ev': (hit_probability * (pot + realistic_win)) - bet
        } 