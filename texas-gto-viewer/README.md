# 德州扑克 GTO 查看器

一个用于分析德州扑克决策是否符合GTO（Game Theory Optimal）策略的工具。

## 功能特点

- 📊 实时分析你的行动与GTO策略的偏离度
- 🎯 支持各种场景下的范围分析
- 📈 可视化展示最优策略
- 🔍 深度分析每个决策点
- 💡 提供改进建议

## 安装

```bash
pip install -r requirements.txt
```

## 使用方法

```bash
streamlit run app.py
```

## 主要功能

1. **翻前范围分析** - 分析不同位置的起手牌范围
2. **翻后决策分析** - 计算翻牌、转牌、河牌的最优策略
3. **下注尺度优化** - 推荐最优下注尺度
4. **偏离度计算** - 量化你的行动与GTO的差异
5. **历史记录回放** - 复盘历史手牌

## 技术架构

- 前端：Streamlit + Plotly
- 后端：Python + NumPy
- 算法：简化的CFR（Counterfactual Regret Minimization） 