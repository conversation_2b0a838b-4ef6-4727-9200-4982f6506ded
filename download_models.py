import os
import requests
from tqdm import tqdm
from modelscope.hub.snapshot_download import snapshot_download

def ensure_dir(dir_path):
    if not os.path.exists(dir_path):
        os.makedirs(dir_path, exist_ok=True)

def download_models():
    # 创建保存目录
    base_dir = "/alidata1/GPT-SoVITS"
    model_dir = os.path.join(base_dir, "GPT_SoVITS/pretrained_models")
    ensure_dir(model_dir)
    
    # ModelScope 模型列表
    models = {
        "chinese-roberta-wwm-ext-large": "damo/nlp_roberta_large-wwm-ext-chinese",
        "chinese-hubert-base": "damo/speech_chinese-hubert-base_modelscope"
    }
    
    for model_name, model_id in models.items():
        save_path = os.path.join(model_dir, model_name)
        if os.path.exists(save_path):
            print(f"{model_name} 已存在，跳过下载")
            continue
            
        print(f"正在下载 {model_name}...")
        try:
            # 下载模型
            snapshot_download(model_id, cache_dir=save_path)
            print(f"{model_name} 下载完成")
        except Exception as e:
            print(f"下载 {model_name} 时出错: {str(e)}")

if __name__ == "__main__":
    # 安装必要的包
    try:
        from modelscope.hub.snapshot_download import snapshot_download
    except ImportError:
        print("正在安装 modelscope...")
        os.system("pip install modelscope")
        
    print("开始下载模型...")
    download_models()
    print("所有模型下载完成！") 
