import os
import subprocess

# 首先安装必要的包
def install_requirements():
    print("正在安装必要的包...")
    packages = ['requests', 'tqdm']
    for package in packages:
        subprocess.check_call(['pip3', 'install', package])

if __name__ == "__main__":
    # 先安装依赖
    install_requirements()
    
    # 然后导入需要的模块
    import requests
    from tqdm import tqdm
    
    def ensure_dir(dir_path):
        if not os.path.exists(dir_path):
            os.makedirs(dir_path, exist_ok=True)

    def download_file(url, filename):
        response = requests.get(url, stream=True)
        total_size = int(response.headers.get('content-length', 0))
        
        with open(filename, 'wb') as file, tqdm(
            desc=filename,
            total=total_size,
            unit='iB',
            unit_scale=True,
            unit_divisor=1024,
        ) as pbar:
            for data in response.iter_content(chunk_size=1024):
                size = file.write(data)
                pbar.update(size)

    def download_models():
        # 创建保存目录
        base_dir = os.path.expanduser("~/Downloads/gpt_sovits_models")
        ensure_dir(base_dir)
        
        # 模型文件列表
        models = {
            "chinese-hubert-base/config.json": "https://huggingface.co/TencentGameMate/chinese-hubert-base/resolve/main/config.json",
            "chinese-hubert-base/pytorch_model.bin": "https://huggingface.co/TencentGameMate/chinese-hubert-base/resolve/main/pytorch_model.bin",
            
            "chinese-roberta-wwm-ext-large/config.json": "https://huggingface.co/hfl/chinese-roberta-wwm-ext-large/resolve/main/config.json",
            "chinese-roberta-wwm-ext-large/pytorch_model.bin": "https://huggingface.co/hfl/chinese-roberta-wwm-ext-large/resolve/main/pytorch_model.bin",
            "chinese-roberta-wwm-ext-large/vocab.txt": "https://huggingface.co/hfl/chinese-roberta-wwm-ext-large/resolve/main/vocab.txt"
        }
        
        for file_path, url in models.items():
            save_path = os.path.join(base_dir, file_path)
            ensure_dir(os.path.dirname(save_path))
            
            if os.path.exists(save_path):
                print(f"{file_path} 已存在，跳过下载")
                continue
                
            print(f"正在下载 {file_path}...")
            try:
                download_file(url, save_path)
                print(f"{file_path} 下载完成")
            except Exception as e:
                print(f"下载 {file_path} 时出错: {str(e)}")

    print("开始下载模型...")
    download_models()
    print("所有模型下载完成！") 