import pygame
import random
from enemy import Enemy

class Room:
    def __init__(self, width, height, room_type="normal"):
        self.width = width
        self.height = height
        self.type = room_type  # normal, item, boss
        self.is_cleared = False  # 房间是否已清理
        self.enemies = []
        self.items = []
        
        # 根据房间类型生成内容
        self.generate_content()
        
        # 加载房间图像
        self.load_images()
    
    def load_images(self):
        # 在实际项目中，这里应该加载房间的图像
        # 由于我们没有实际的图像文件，这里创建一个简单的表面
        self.floor_image = self.create_floor_surface()
        self.wall_image = self.create_wall_surface()
    
    def create_floor_surface(self):
        # 创建一个简单的表面作为地板
        surface = pygame.Surface((self.width, self.height))
        
        # 根据房间类型设置不同的地板颜色
        if self.type == "normal":
            color = (50, 50, 50)  # 深灰色
        elif self.type == "item":
            color = (50, 50, 80)  # 带蓝色的深灰
        else:  # boss
            color = (80, 50, 50)  # 带红色的深灰
        
        surface.fill(color)
        
        # 添加一些随机的细节
        for _ in range(100):
            x = random.randint(0, self.width - 1)
            y = random.randint(0, self.height - 1)
            brightness = random.randint(-20, 20)
            detail_color = (
                max(0, min(255, color[0] + brightness)),
                max(0, min(255, color[1] + brightness)),
                max(0, min(255, color[2] + brightness))
            )
            pygame.draw.rect(surface, detail_color, (x, y, 3, 3))
        
        return surface
    
    def create_wall_surface(self):
        # 创建一个简单的表面作为墙壁
        wall_width = 50  # 墙壁宽度
        surface = pygame.Surface((self.width, self.height), pygame.SRCALPHA)
        
        # 绘制四周的墙壁
        wall_color = (30, 30, 30)
        pygame.draw.rect(surface, wall_color, (0, 0, self.width, wall_width))  # 上墙
        pygame.draw.rect(surface, wall_color, (0, self.height - wall_width, self.width, wall_width))  # 下墙
        pygame.draw.rect(surface, wall_color, (0, 0, wall_width, self.height))  # 左墙
        pygame.draw.rect(surface, wall_color, (self.width - wall_width, 0, wall_width, self.height))  # 右墙
        
        # 在墙上添加一些细节
        for _ in range(50):
            wall_side = random.randint(0, 3)  # 0=上, 1=右, 2=下, 3=左
            if wall_side == 0:
                x = random.randint(0, self.width - 1)
                y = random.randint(0, wall_width - 1)
            elif wall_side == 1:
                x = random.randint(self.width - wall_width, self.width - 1)
                y = random.randint(0, self.height - 1)
            elif wall_side == 2:
                x = random.randint(0, self.width - 1)
                y = random.randint(self.height - wall_width, self.height - 1)
            else:
                x = random.randint(0, wall_width - 1)
                y = random.randint(0, self.height - 1)
            
            brightness = random.randint(-10, 10)
            detail_color = (
                max(0, min(255, wall_color[0] + brightness)),
                max(0, min(255, wall_color[1] + brightness)),
                max(0, min(255, wall_color[2] + brightness))
            )
            pygame.draw.rect(surface, detail_color, (x, y, 5, 5))
        
        return surface
    
    def generate_content(self):
        # 根据房间类型生成内容
        if self.type == "normal":
            # 普通房间：生成2-4个敌人
            num_enemies = random.randint(2, 4)
            for _ in range(num_enemies):
                self.spawn_enemy()
        elif self.type == "boss":
            # Boss房间：生成一个强大的敌人
            boss = Enemy(self.width // 2, self.height // 2, "boss")
            self.enemies.append(boss)
        # 物品房间不生成敌人，只有物品
    
    def spawn_enemy(self):
        # 在房间中随机位置生成敌人
        padding = 100  # 与边缘的距离
        x = random.randint(padding, self.width - padding)
        y = random.randint(padding, self.height - padding)
        
        # 随机选择敌人类型
        enemy_type = random.choice(["fly", "spider"])
        enemy = Enemy(x, y, enemy_type)
        self.enemies.append(enemy)
    
    def add_item(self, item):
        self.items.append(item)
    
    def update(self, player):
        # 更新所有敌人
        for enemy in self.enemies[:]:
            enemy.update(player)
            if enemy.health <= 0:
                self.enemies.remove(enemy)
        
        # 检查房间是否已清理
        if not self.enemies and not self.is_cleared:
            self.is_cleared = True
            # 在实际项目中，这里可以播放房间清理的音效
    
    def render(self, screen):
        # 渲染地板
        screen.blit(self.floor_image, (0, 0))
        
        # 渲染物品
        for item in self.items:
            item.render(screen)
        
        # 渲染敌人
        for enemy in self.enemies:
            enemy.render(screen)
        
        # 渲染墙壁
        screen.blit(self.wall_image, (0, 0))
        
        # 如果房间已清理，渲染门
        if self.is_cleared:
            self.render_doors(screen)
    
    def render_doors(self, screen):
        # 渲染门
        door_width = 60
        door_height = 40
        door_color = (139, 69, 19)  # 棕色
        
        # 下门
        pygame.draw.rect(screen, door_color, (self.width // 2 - door_width // 2, self.height - 50, door_width, door_height))
        
        # 添加门的提示文本
        font = pygame.font.Font(None, 24)
        text = font.render("按E键进入下一个房间", True, (255, 255, 255))
        text_rect = text.get_rect(center=(self.width // 2, self.height - 25))
        screen.blit(text, text_rect) 