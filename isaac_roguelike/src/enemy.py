import pygame
import random
import math

class Enemy:
    def __init__(self, x, y, enemy_type="fly"):
        self.x = x
        self.y = y
        self.type = enemy_type
        
        # 根据敌人类型设置属性
        if self.type == "fly":
            self.width = 30
            self.height = 30
            self.speed = 2
            self.health = 10
            self.damage = 1
            self.color = (128, 128, 128)  # 灰色
            self.movement_pattern = "random"
        elif self.type == "spider":
            self.width = 35
            self.height = 35
            self.speed = 3
            self.health = 15
            self.damage = 1
            self.color = (0, 0, 0)  # 黑色
            self.movement_pattern = "jump"
            self.jump_cooldown = 0
            self.jump_target_x = self.x
            self.jump_target_y = self.y
        elif self.type == "boss":
            self.width = 80
            self.height = 80
            self.speed = 1.5
            self.health = 100
            self.damage = 2
            self.color = (139, 0, 0)  # 深红色
            self.movement_pattern = "follow"
            self.attack_cooldown = 0
        
        # 创建敌人图像
        self.image = self.create_enemy_surface()
        
        # 动画相关
        self.animation_frame = 0
        self.animation_speed = 0.2
    
    def create_enemy_surface(self):
        surface = pygame.Surface((self.width, self.height), pygame.SRCALPHA)
        
        if self.type == "fly":
            # 绘制苍蝇形状
            pygame.draw.circle(surface, self.color, (self.width // 2, self.height // 2), self.width // 2)
            # 添加翅膀
            wing_color = (200, 200, 200)
            pygame.draw.ellipse(surface, wing_color, (0, self.height // 4, self.width // 2, self.height // 2))
            pygame.draw.ellipse(surface, wing_color, (self.width // 2, self.height // 4, self.width // 2, self.height // 2))
            # 添加眼睛
            eye_color = (255, 0, 0)
            pygame.draw.circle(surface, eye_color, (self.width // 3, self.height // 3), 3)
            pygame.draw.circle(surface, eye_color, (self.width * 2 // 3, self.height // 3), 3)
        
        elif self.type == "spider":
            # 绘制蜘蛛身体
            pygame.draw.circle(surface, self.color, (self.width // 2, self.height // 2), self.width // 2)
            # 添加腿
            leg_color = (50, 50, 50)
            for i in range(8):
                angle = i * math.pi / 4
                leg_length = self.width // 2 + 10
                end_x = self.width // 2 + math.cos(angle) * leg_length
                end_y = self.height // 2 + math.sin(angle) * leg_length
                pygame.draw.line(surface, leg_color, (self.width // 2, self.height // 2), (end_x, end_y), 2)
            # 添加眼睛
            eye_color = (255, 0, 0)
            pygame.draw.circle(surface, eye_color, (self.width // 3, self.height // 3), 3)
            pygame.draw.circle(surface, eye_color, (self.width * 2 // 3, self.height // 3), 3)
        
        elif self.type == "boss":
            # 绘制Boss形状
            pygame.draw.circle(surface, self.color, (self.width // 2, self.height // 2), self.width // 2)
            # 添加细节
            detail_color = (200, 0, 0)
            pygame.draw.circle(surface, detail_color, (self.width // 2, self.height // 2), self.width // 3)
            # 添加眼睛
            eye_color = (255, 255, 0)
            pygame.draw.circle(surface, eye_color, (self.width // 3, self.height // 3), 8)
            pygame.draw.circle(surface, eye_color, (self.width * 2 // 3, self.height // 3), 8)
            pygame.draw.circle(surface, (0, 0, 0), (self.width // 3, self.height // 3), 4)
            pygame.draw.circle(surface, (0, 0, 0), (self.width * 2 // 3, self.height // 3), 4)
            # 添加嘴巴
            mouth_color = (0, 0, 0)
            pygame.draw.arc(surface, mouth_color, (self.width // 4, self.height // 2, self.width // 2, self.height // 3), 0, math.pi, 3)
        
        return surface
    
    def update(self, player):
        # 更新动画
        self.animation_frame += self.animation_speed
        
        # 根据移动模式更新位置
        if self.movement_pattern == "random":
            # 随机移动
            if random.random() < 0.05:  # 5%的几率改变方向
                angle = random.uniform(0, 2 * math.pi)
                self.dx = math.cos(angle) * self.speed
                self.dy = math.sin(angle) * self.speed
            else:
                # 继续当前方向，如果没有方向则随机设置一个
                if not hasattr(self, 'dx') or not hasattr(self, 'dy'):
                    angle = random.uniform(0, 2 * math.pi)
                    self.dx = math.cos(angle) * self.speed
                    self.dy = math.sin(angle) * self.speed
            
            # 更新位置
            self.x += self.dx
            self.y += self.dy
            
            # 边界检查
            padding = 50
            if self.x < padding or self.x > 800 - padding:
                self.dx *= -1
            if self.y < padding or self.y > 600 - padding:
                self.dy *= -1
        
        elif self.movement_pattern == "jump":
            # 跳跃移动
            if self.jump_cooldown <= 0:
                # 设置新的跳跃目标
                self.jump_target_x = player.x + random.randint(-100, 100)
                self.jump_target_y = player.y + random.randint(-100, 100)
                
                # 边界检查
                padding = 50
                self.jump_target_x = max(padding, min(800 - padding, self.jump_target_x))
                self.jump_target_y = max(padding, min(600 - padding, self.jump_target_y))
                
                # 设置跳跃冷却
                self.jump_cooldown = 60  # 60帧
            else:
                self.jump_cooldown -= 1
                
                # 逐渐移动到目标位置
                if self.jump_cooldown < 30:  # 只在冷却的后半部分移动
                    dx = (self.jump_target_x - self.x) / 10
                    dy = (self.jump_target_y - self.y) / 10
                    self.x += dx
                    self.y += dy
        
        elif self.movement_pattern == "follow":
            # 跟随玩家
            dx = player.x - self.x
            dy = player.y - self.y
            distance = math.sqrt(dx * dx + dy * dy)
            
            if distance > 0:
                dx /= distance
                dy /= distance
            
            self.x += dx * self.speed
            self.y += dy * self.speed
            
            # Boss攻击逻辑
            if self.attack_cooldown <= 0:
                # 在实际项目中，这里可以添加Boss的攻击逻辑
                self.attack_cooldown = 120  # 120帧
            else:
                self.attack_cooldown -= 1
    
    def take_damage(self, amount):
        self.health -= amount
        if self.health < 0:
            self.health = 0
    
    def render(self, screen):
        # 根据动画帧选择图像
        image = self.image
        
        # 如果是苍蝇，添加翅膀动画
        if self.type == "fly":
            # 创建一个新的表面用于动画
            surface = pygame.Surface((self.width, self.height), pygame.SRCALPHA)
            surface.blit(image, (0, 0))
            
            # 翅膀动画
            wing_offset = math.sin(self.animation_frame) * 3
            wing_color = (200, 200, 200)
            pygame.draw.ellipse(surface, wing_color, (0, self.height // 4 + wing_offset, self.width // 2, self.height // 2))
            pygame.draw.ellipse(surface, wing_color, (self.width // 2, self.height // 4 - wing_offset, self.width // 2, self.height // 2))
            
            image = surface
        
        # 渲染敌人
        screen.blit(image, (self.x - self.width // 2, self.y - self.height // 2))
        
        # 渲染生命条
        self.render_health_bar(screen)
    
    def render_health_bar(self, screen):
        # 只有Boss显示生命条
        if self.type == "boss":
            bar_width = 60
            bar_height = 8
            bar_x = self.x - bar_width // 2
            bar_y = self.y - self.height // 2 - 15
            
            # 背景
            pygame.draw.rect(screen, (255, 0, 0), (bar_x, bar_y, bar_width, bar_height))
            
            # 当前生命
            health_width = max(0, (self.health / 100) * bar_width)
            pygame.draw.rect(screen, (0, 255, 0), (bar_x, bar_y, health_width, bar_height)) 