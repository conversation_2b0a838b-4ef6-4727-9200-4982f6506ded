import pygame
import math
from projectile import Projectile

class Player:
    def __init__(self, x, y):
        self.x = x
        self.y = y
        self.width = 40
        self.height = 40
        self.speed = 5
        self.health = 6  # 以撒风格的心形生命值，每个心是2点
        self.max_health = 6
        self.damage = 3.5
        self.tear_rate = 10  # 发射间隔帧数
        self.tear_counter = 0
        self.direction = "down"  # 朝向：up, down, left, right
        self.items = []  # 收集的物品
        self.projectiles = []  # 玩家发射的眼泪/子弹
        self.invincible = 0  # 无敌时间计数器
        
        # 加载玩家图像
        self.load_images()
        
        # 加载音效
        self.load_sounds()
    
    def load_images(self):
        # 在实际项目中，这里应该加载玩家的图像
        # 由于我们没有实际的图像文件，这里创建一个简单的表面
        self.images = {
            "down": self.create_player_surface((255, 255, 255)),
            "up": self.create_player_surface((255, 255, 255)),
            "left": self.create_player_surface((255, 255, 255)),
            "right": self.create_player_surface((255, 255, 255)),
            "hurt": self.create_player_surface((255, 0, 0))
        }
    
    def load_sounds(self):
        self.sounds = {}
        sound_files = {
            "hurt": "../assets/sounds/hurt.wav",
            "shoot": "../assets/sounds/shoot.wav",
            "item_pickup": "../assets/sounds/item_pickup.wav",
        }
        
        # 尝试加载音效，如果文件不存在则跳过
        for sound_name, sound_path in sound_files.items():
            try:
                self.sounds[sound_name] = pygame.mixer.Sound(sound_path)
                self.sounds[sound_name].set_volume(0.5)
            except:
                print(f"无法加载音效: {sound_path}")
    
    def create_player_surface(self, color):
        # 创建一个简单的表面作为玩家图像
        surface = pygame.Surface((self.width, self.height), pygame.SRCALPHA)
        pygame.draw.circle(surface, color, (self.width // 2, self.height // 2), self.width // 2)
        pygame.draw.circle(surface, (0, 0, 0), (self.width // 2 - 8, self.height // 2 - 8), 5)  # 左眼
        pygame.draw.circle(surface, (0, 0, 0), (self.width // 2 + 8, self.height // 2 - 8), 5)  # 右眼
        return surface
    
    def handle_event(self, event):
        # 处理射击
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_UP:
                self.shoot("up")
            elif event.key == pygame.K_DOWN:
                self.shoot("down")
            elif event.key == pygame.K_LEFT:
                self.shoot("left")
            elif event.key == pygame.K_RIGHT:
                self.shoot("right")
    
    def update(self, room):
        # 处理移动
        keys = pygame.key.get_pressed()
        dx, dy = 0, 0
        
        if keys[pygame.K_w]:
            dy -= self.speed
            self.direction = "up"
        if keys[pygame.K_s]:
            dy += self.speed
            self.direction = "down"
        if keys[pygame.K_a]:
            dx -= self.speed
            self.direction = "left"
        if keys[pygame.K_d]:
            dx += self.speed
            self.direction = "right"
        
        # 对角线移动速度修正
        if dx != 0 and dy != 0:
            dx *= 0.7071  # 1/sqrt(2)
            dy *= 0.7071
        
        # 边界检查
        new_x = self.x + dx
        new_y = self.y + dy
        
        # 房间边界
        room_padding = 50  # 房间边缘的内边距
        if new_x - self.width // 2 > room_padding and new_x + self.width // 2 < room.width - room_padding:
            self.x = new_x
        if new_y - self.height // 2 > room_padding and new_y + self.height // 2 < room.height - room_padding:
            self.y = new_y
        
        # 更新射击冷却
        if self.tear_counter > 0:
            self.tear_counter -= 1
        
        # 更新无敌时间
        if self.invincible > 0:
            self.invincible -= 1
        
        # 更新投射物
        for projectile in self.projectiles[:]:
            projectile.update()
            # 移除超出屏幕的投射物
            if (projectile.x < 0 or projectile.x > room.width or 
                projectile.y < 0 or projectile.y > room.height):
                self.projectiles.remove(projectile)
        
        # 检查与敌人的碰撞
        if not self.invincible:
            for enemy in room.enemies:
                if self.collides_with(enemy):
                    self.take_damage(1)
                    break
        
        # 检查与物品的碰撞
        for item in room.items[:]:
            if self.collides_with(item):
                self.pickup_item(item)
                room.items.remove(item)
        
        # 检查投射物与敌人的碰撞
        for projectile in self.projectiles[:]:
            for enemy in room.enemies[:]:
                if projectile.collides_with(enemy):
                    enemy.take_damage(self.damage)
                    if projectile in self.projectiles:  # 确保投射物还在列表中
                        self.projectiles.remove(projectile)
                    break
    
    def shoot(self, direction=None):
        if self.tear_counter > 0:
            return
        
        if direction is None:
            direction = self.direction
        
        # 设置射击方向
        dx, dy = 0, 0
        if direction == "up":
            dy = -1
        elif direction == "down":
            dy = 1
        elif direction == "left":
            dx = -1
        elif direction == "right":
            dx = 1
        
        # 创建投射物
        projectile_x = self.x
        projectile_y = self.y
        
        # 检查是否有硫磺火物品
        has_sulfur = any(item.name == "sulfur" for item in self.items)
        
        if has_sulfur:
            # 硫磺火效果：发射黄色的火焰
            projectile = Projectile(projectile_x, projectile_y, dx, dy, "sulfur")
        else:
            # 普通眼泪
            projectile = Projectile(projectile_x, projectile_y, dx, dy, "tear")
        
        self.projectiles.append(projectile)
        
        # 播放射击音效
        if "shoot" in self.sounds:
            self.sounds["shoot"].play()
        
        # 重置射击冷却
        self.tear_counter = self.tear_rate
    
    def take_damage(self, amount):
        if self.invincible > 0:
            return
        
        self.health -= amount
        if self.health < 0:
            self.health = 0
        
        # 设置无敌时间
        self.invincible = 30  # 30帧无敌时间
        
        # 播放受伤音效
        if "hurt" in self.sounds:
            self.sounds["hurt"].play()
    
    def pickup_item(self, item):
        self.items.append(item)
        
        # 应用物品效果
        if item.name == "sulfur":
            # 硫磺火效果：增加伤害
            self.damage += 2
        
        # 播放拾取音效
        if "item_pickup" in self.sounds:
            self.sounds["item_pickup"].play()
    
    def collides_with(self, entity):
        # 简单的圆形碰撞检测
        distance = math.sqrt((self.x - entity.x) ** 2 + (self.y - entity.y) ** 2)
        return distance < (self.width // 2 + entity.width // 2)
    
    def render(self, screen):
        # 选择要渲染的图像
        if self.invincible > 0 and self.invincible % 4 < 2:  # 闪烁效果
            image = self.images["hurt"]
        else:
            image = self.images[self.direction]
        
        # 渲染玩家
        screen.blit(image, (self.x - self.width // 2, self.y - self.height // 2))
        
        # 渲染投射物
        for projectile in self.projectiles:
            projectile.render(screen) 