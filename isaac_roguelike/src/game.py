import pygame
import random
from player import Player
from room import Room
from item import Item

class Game:
    def __init__(self, screen):
        self.screen = screen
        self.screen_width = screen.get_width()
        self.screen_height = screen.get_height()
        
        # 游戏状态
        self.state = "playing"  # playing, game_over, victory
        
        # 创建玩家
        self.player = Player(self.screen_width // 2, self.screen_height // 2)
        
        # 创建房间
        self.rooms = []
        self.current_room_index = 0
        self.generate_rooms()
        
        # 加载音效
        self.load_sounds()
        
        # 加载背景音乐
        self.load_music()
        # pygame.mixer.music.play(-1)  # 循环播放
    
    def load_sounds(self):
        self.sounds = {}
        sound_files = {
            "hurt": "../assets/sounds/hurt.wav",
            "shoot": "../assets/sounds/shoot.wav",
            "item_pickup": "../assets/sounds/item_pickup.wav",
            "door": "../assets/sounds/door.wav",
        }
        
        # 尝试加载音效，如果文件不存在则跳过
        for sound_name, sound_path in sound_files.items():
            try:
                self.sounds[sound_name] = pygame.mixer.Sound(sound_path)
                self.sounds[sound_name].set_volume(0.5)
            except:
                print(f"无法加载音效: {sound_path}")
    
    def load_music(self):
        try:
            pygame.mixer.music.load("../assets/music/background.wav")
            pygame.mixer.music.set_volume(0.3)
        except:
            print("背景音乐加载失败")
    
    def generate_rooms(self):
        # 创建三个房间
        room_types = ["normal", "item", "boss"]
        
        for i, room_type in enumerate(room_types):
            room = Room(self.screen_width, self.screen_height, room_type)
            
            # 在物品房间添加硫磺火道具
            if room_type == "item":
                sulfur_item = Item("sulfur", self.screen_width // 2, self.screen_height // 2)
                room.add_item(sulfur_item)
            
            self.rooms.append(room)
    
    def handle_event(self, event):
        # 处理键盘输入
        if event.type == pygame.KEYDOWN:
            if event.key == pygame.K_ESCAPE:
                pygame.quit()
                exit()
            
            # 在房间之间移动
            if event.key == pygame.K_e and self.rooms[self.current_room_index].is_cleared:
                self.next_room()
        
        # 将事件传递给玩家处理
        self.player.handle_event(event)
    
    def update(self):
        # 更新当前房间
        current_room = self.rooms[self.current_room_index]
        current_room.update(self.player)
        
        # 更新玩家
        self.player.update(current_room)
        
        # 检查玩家是否死亡
        if self.player.health <= 0:
            self.state = "game_over"
        
        # 检查是否通关（击败最后一个房间的boss）
        if self.current_room_index == len(self.rooms) - 1 and current_room.is_cleared:
            self.state = "victory"
    
    def next_room(self):
        if self.current_room_index < len(self.rooms) - 1:
            if "door" in self.sounds:
                self.sounds["door"].play()
            self.current_room_index += 1
            # 将玩家放置在新房间的中心
            self.player.x = self.screen_width // 2
            self.player.y = self.screen_height // 2
    
    def render(self):
        # 渲染当前房间
        self.rooms[self.current_room_index].render(self.screen)
        
        # 渲染玩家
        self.player.render(self.screen)
        
        # 渲染UI
        self.render_ui()
        
        # 渲染游戏状态
        if self.state == "game_over":
            self.render_game_over()
        elif self.state == "victory":
            self.render_victory()
    
    def render_ui(self):
        # 渲染生命值
        font = pygame.font.Font(None, 36)
        health_text = font.render(f"生命: {self.player.health}", True, (255, 0, 0))
        self.screen.blit(health_text, (20, 20))
        
        # 渲染当前房间信息
        room_info = f"房间 {self.current_room_index + 1}/{len(self.rooms)}"
        room_text = font.render(room_info, True, (255, 255, 255))
        self.screen.blit(room_text, (self.screen_width - 150, 20))
        
        # 渲染物品信息
        if self.player.items:
            item_text = font.render(f"物品: {', '.join(item.name for item in self.player.items)}", True, (255, 255, 0))
            self.screen.blit(item_text, (20, 60))
    
    def render_game_over(self):
        overlay = pygame.Surface((self.screen_width, self.screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 180))
        self.screen.blit(overlay, (0, 0))
        
        font = pygame.font.Font(None, 72)
        text = font.render("游戏结束", True, (255, 0, 0))
        text_rect = text.get_rect(center=(self.screen_width // 2, self.screen_height // 2))
        self.screen.blit(text, text_rect)
        
        font = pygame.font.Font(None, 36)
        restart_text = font.render("按ESC键退出", True, (255, 255, 255))
        restart_rect = restart_text.get_rect(center=(self.screen_width // 2, self.screen_height // 2 + 50))
        self.screen.blit(restart_text, restart_rect)
    
    def render_victory(self):
        overlay = pygame.Surface((self.screen_width, self.screen_height), pygame.SRCALPHA)
        overlay.fill((0, 0, 0, 180))
        self.screen.blit(overlay, (0, 0))
        
        font = pygame.font.Font(None, 72)
        text = font.render("胜利！", True, (0, 255, 0))
        text_rect = text.get_rect(center=(self.screen_width // 2, self.screen_height // 2))
        self.screen.blit(text, text_rect)
        
        font = pygame.font.Font(None, 36)
        restart_text = font.render("按ESC键退出", True, (255, 255, 255))
        restart_rect = restart_text.get_rect(center=(self.screen_width // 2, self.screen_height // 2 + 50))
        self.screen.blit(restart_text, restart_rect) 