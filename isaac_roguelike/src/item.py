import pygame
import math

class Item:
    def __init__(self, name, x, y):
        self.name = name
        self.x = x
        self.y = y
        self.width = 30
        self.height = 30
        self.hover_offset = 0
        self.hover_speed = 0.05
        
        # 加载物品图像
        self.image = self.create_item_surface()
    
    def create_item_surface(self):
        surface = pygame.Surface((self.width, self.height), pygame.SRCALPHA)
        
        if self.name == "sulfur":
            # 绘制硫磺火
            # 基础形状
            pygame.draw.circle(surface, (255, 255, 0), (self.width // 2, self.height // 2), self.width // 2)  # 黄色圆形
            
            # 添加火焰效果
            flame_color = (255, 128, 0)  # 橙色
            points = [
                (self.width // 2, 0),
                (self.width // 4, self.height // 2),
                (self.width // 2, self.height),
                (self.width * 3 // 4, self.height // 2)
            ]
            pygame.draw.polygon(surface, flame_color, points)
            
            # 添加中心细节
            pygame.draw.circle(surface, (255, 200, 0), (self.width // 2, self.height // 2), self.width // 4)
        
        # 可以在这里添加更多物品类型
        
        return surface
    
    def update(self):
        # 更新悬浮动画
        self.hover_offset = math.sin(pygame.time.get_ticks() * self.hover_speed) * 3
    
    def render(self, screen):
        # 更新悬浮动画
        self.update()
        
        # 渲染物品
        screen.blit(self.image, (self.x - self.width // 2, self.y - self.height // 2 + self.hover_offset))
        
        # 添加光晕效果
        glow_radius = self.width // 2 + 5 + abs(self.hover_offset)
        glow_surface = pygame.Surface((glow_radius * 2, glow_radius * 2), pygame.SRCALPHA)
        
        # 根据物品类型设置光晕颜色
        if self.name == "sulfur":
            glow_color = (255, 255, 0, 100)  # 半透明黄色
        else:
            glow_color = (255, 255, 255, 100)  # 半透明白色
        
        # 绘制光晕
        pygame.draw.circle(glow_surface, glow_color, (glow_radius, glow_radius), glow_radius)
        
        # 渲染光晕
        screen.blit(glow_surface, (self.x - glow_radius, self.y - glow_radius + self.hover_offset), special_flags=pygame.BLEND_ALPHA_SDL2)
        
        # 渲染物品名称
        font = pygame.font.Font(None, 20)
        text = font.render(self.get_display_name(), True, (255, 255, 255))
        text_rect = text.get_rect(center=(self.x, self.y + self.height // 2 + 15 + self.hover_offset))
        screen.blit(text, text_rect)
    
    def get_display_name(self):
        # 返回物品的显示名称
        if self.name == "sulfur":
            return "硫磺火"
        # 可以在这里添加更多物品的显示名称
        return self.name 