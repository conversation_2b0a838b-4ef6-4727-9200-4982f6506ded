import pygame
import math
import random

class Projectile:
    def __init__(self, x, y, dx, dy, projectile_type="tear"):
        self.x = x
        self.y = y
        self.width = 16
        self.height = 16
        self.speed = 10
        self.dx = dx
        self.dy = dy
        self.type = projectile_type
        
        # 根据类型设置属性
        if self.type == "sulfur":
            self.color = (255, 255, 0)  # 黄色火焰
            self.damage = 7  # 硫磺火伤害更高
            self.width = 20
            self.height = 20
        else:  # 默认为眼泪
            self.color = (0, 191, 255)  # 淡蓝色眼泪
            self.damage = 3.5
        
        # 创建投射物图像
        self.image = self.create_projectile_surface()
    
    def create_projectile_surface(self):
        surface = pygame.Surface((self.width, self.height), pygame.SRCALPHA)
        
        if self.type == "sulfur":
            # 绘制火焰形状
            points = [
                (self.width // 2, 0),
                (self.width // 4, self.height // 2),
                (self.width // 2, self.height),
                (self.width * 3 // 4, self.height // 2)
            ]
            pygame.draw.polygon(surface, self.color, points)
            # 添加一些细节
            pygame.draw.circle(surface, (255, 128, 0), (self.width // 2, self.height // 2), self.width // 4)
        else:
            # 绘制眼泪形状
            pygame.draw.circle(surface, self.color, (self.width // 2, self.height // 2), self.width // 2)
            # 添加高光
            pygame.draw.circle(surface, (255, 255, 255), (self.width // 2 - 3, self.height // 2 - 3), 2)
        
        return surface
    
    def update(self):
        # 更新位置
        self.x += self.dx * self.speed
        self.y += self.dy * self.speed
    
    def collides_with(self, entity):
        # 简单的圆形碰撞检测
        distance = math.sqrt((self.x - entity.x) ** 2 + (self.y - entity.y) ** 2)
        return distance < (self.width // 2 + entity.width // 2)
    
    def render(self, screen):
        # 渲染投射物
        screen.blit(self.image, (self.x - self.width // 2, self.y - self.height // 2))
        
        # 如果是硫磺火，添加一些粒子效果
        if self.type == "sulfur":
            for _ in range(2):
                particle_x = self.x + random.randint(-5, 5)
                particle_y = self.y + random.randint(-5, 5)
                particle_size = random.randint(2, 5)
                pygame.draw.circle(screen, (255, 128, 0), (int(particle_x), int(particle_y)), particle_size) 