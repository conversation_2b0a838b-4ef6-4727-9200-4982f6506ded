<!DOCTYPE html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>AI外呼催收系统2.0流程图</title>
    <style>
      :root {
        --primary-color: #4361ee;
        --secondary-color: #3a0ca3;
        --accent-color: #7209b7;
        --light-color: #f8f9fa;
        --dark-color: #212529;
        --success-color: #38b000;
        --warning-color: #ff9e00;
        --danger-color: #d90429;
        --info-color: #4cc9f0;

        --agent-bg: #fff0f0;
        --agent-border: #ff6b6b;
        --system-bg: #e3f5ff;
        --system-border: #4dabf7;
        --category-bg: #f3e5f5;
        --category-border: #9c27b0;
        --script-bg: #e8f5e9;
        --script-border: #4caf50;
        --hook-bg: #fff8e1;
        --hook-border: #ffca28;
        --rag-bg: #e8f4ff;
        --rag-border: #2196f3;
        --tag-bg: #f3e2c7;
        --tag-border: #f57c00;
        --analysis-bg: #e5e1f5;
        --analysis-border: #673ab7;
        --faq-content-bg: #ddf4ff;
        --faq-content-border: #00b0ff;
        --external-tag-bg: #ffecb3;
        --external-tag-border: #ffa000;
      }

      body {
        font-family: "PingFang SC", "Microsoft YaHei", -apple-system,
          BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell,
          "Open Sans", "Helvetica Neue", sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f5f5f5;
        color: var(--dark-color);
        line-height: 1.5;
      }

      .container {
        max-width: 1100px;
        margin: 0 auto;
        background-color: white;
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        border-radius: 10px;
        padding: 30px;
      }

      h1 {
        text-align: center;
        color: var(--primary-color);
        margin-bottom: 30px;
        font-weight: 600;
        position: relative;
        padding-bottom: 15px;
      }

      h1:after {
        content: "";
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 3px;
        background: linear-gradient(
          to right,
          var(--primary-color),
          var(--accent-color)
        );
      }

      .svg-container {
        width: 100%;
        overflow-x: auto;
        margin: 20px 0;
      }

      svg {
        display: block;
        margin: 0 auto;
      }

      .legend {
        display: flex;
        justify-content: center;
        flex-wrap: wrap;
        gap: 15px;
        margin-top: 30px;
        padding: 15px;
        border-radius: 8px;
        background-color: #f8f9fa;
      }

      .legend-item {
        display: flex;
        align-items: center;
        margin-right: 15px;
      }

      .legend-color {
        width: 20px;
        height: 20px;
        margin-right: 8px;
        border-radius: 4px;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <h1>AI外呼催收系统2.0流程图</h1>

      <div class="svg-container">
        <svg
          width="1080"
          height="1500"
          viewBox="0 0 1080 1500"
          xmlns="http://www.w3.org/2000/svg"
        >
          <!-- 定义箭头样式 -->
          <defs>
            <marker
              id="arrowhead"
              markerWidth="10"
              markerHeight="7"
              refX="9"
              refY="3.5"
              orient="auto"
            >
              <polygon points="0 0, 10 3.5, 0 7" fill="#6c757d" />
            </marker>

            <!-- 阴影效果 -->
            <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
              <feDropShadow
                dx="2"
                dy="3"
                stdDeviation="3"
                flood-opacity="0.15"
              />
            </filter>

            <!-- 渐变定义 -->
            <linearGradient
              id="agentGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" style="stop-color: #fff0f0; stop-opacity: 1" />
              <stop
                offset="100%"
                style="stop-color: #ffcbcb; stop-opacity: 0.8"
              />
            </linearGradient>

            <linearGradient
              id="systemGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" style="stop-color: #e3f5ff; stop-opacity: 1" />
              <stop
                offset="100%"
                style="stop-color: #b4daff; stop-opacity: 0.8"
              />
            </linearGradient>

            <linearGradient
              id="categoryGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="0%"
            >
              <stop offset="0%" style="stop-color: #e1bee7; stop-opacity: 1" />
              <stop
                offset="100%"
                style="stop-color: #ce93d8; stop-opacity: 0.8"
              />
            </linearGradient>

            <linearGradient
              id="scriptGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" style="stop-color: #e8f5e9; stop-opacity: 1" />
              <stop
                offset="100%"
                style="stop-color: #c8e6c9; stop-opacity: 0.8"
              />
            </linearGradient>

            <linearGradient
              id="hookGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" style="stop-color: #fff8e1; stop-opacity: 1" />
              <stop
                offset="100%"
                style="stop-color: #ffe57f; stop-opacity: 0.8"
              />
            </linearGradient>
            
            <linearGradient
              id="ragGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" style="stop-color: #e8f4ff; stop-opacity: 1" />
              <stop
                offset="100%"
                style="stop-color: #bbdefb; stop-opacity: 0.8"
              />
            </linearGradient>
            
            <linearGradient
              id="tagGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" style="stop-color: #f3e2c7; stop-opacity: 1" />
              <stop
                offset="100%"
                style="stop-color: #ffe0b2; stop-opacity: 0.8"
              />
            </linearGradient>
            
            <linearGradient
              id="analysisGradient"
              x1="0%"
              y1="0%"
              x2="100%"
              y2="100%"
            >
              <stop offset="0%" style="stop-color: #e5e1f5; stop-opacity: 1" />
              <stop
                offset="100%"
                style="stop-color: #d1c4e9; stop-opacity: 0.8"
              />
            </linearGradient>
          </defs>

          <!-- 1. 开始催收外呼 -->
          <rect
            x="440"
            y="50"
            width="200"
            height="60"
            rx="30"
            fill="#4361ee"
            filter="url(#shadow)"
          />
          <text
            x="540"
            y="85"
            font-size="18"
            text-anchor="middle"
            fill="white"
            font-weight="bold"
          >
            开始催收外呼
          </text>

          <!-- 2. 核身环节 -->
          <rect
            x="440"
            y="150"
            width="200"
            height="60"
            rx="8"
            fill="#f8f9fa"
            stroke="#6c757d"
            stroke-width="2"
            filter="url(#shadow)"
          />
          <text
            x="540"
            y="185"
            font-size="18"
            text-anchor="middle"
            font-weight="bold"
          >
            核身环节
          </text>

          <!-- 连接线：开始到核身 -->
          <line
            x1="540"
            y1="110"
            x2="540"
            y2="150"
            stroke="#6c757d"
            stroke-width="2"
            marker-end="url(#arrowhead)"
          />

          <!-- 3. 告知环节 -->
          <rect
            x="440"
            y="250"
            width="200"
            height="100"
            rx="8"
            fill="#f8f9fa"
            stroke="#6c757d"
            stroke-width="2"
            filter="url(#shadow)"
          />
          <text
            x="540"
            y="275"
            font-size="18"
            text-anchor="middle"
            font-weight="bold"
          >
            告知环节
          </text>
          <text x="540" y="300" font-size="14" text-anchor="middle">
            欠款金额/逾期天数
          </text>
          
          <!-- 客户标签 - 优化为外部传入 -->
          <rect
            x="465"
            y="315"
            width="150"
            height="25"
            rx="12"
            fill="url(#tagGradient)"
            stroke="var(--tag-border)"
            stroke-width="1"
          />
          <text
            x="540"
            y="332"
            font-size="12"
            text-anchor="middle"
            fill="#555"
          >
            客户标签: 老赖/小白/...
          </text>
          
          <!-- 连接线：核身到告知 -->
          <line
            x1="540"
            y1="210"
            x2="540"
            y2="250"
            stroke="#6c757d"
            stroke-width="2"
            marker-end="url(#arrowhead)"
          />
          <!-- 核身通过标签 -->
          <rect
            x="545"
            y="220"
            width="70"
            height="20"
            rx="10"
            fill="white"
            stroke="#6c757d"
            stroke-width="1"
          />
          <text
            x="580"
            y="235"
            font-size="12"
            text-anchor="middle"
            fill="#6c757d"
          >
            核身通过
          </text>

          <!-- 连接线：告知到RAG召回 -->
          <line
            x1="540"
            y1="350"
            x2="540"
            y2="380"
            stroke="#6c757d"
            stroke-width="2"
            marker-end="url(#arrowhead)"
          />
          
          <!-- RAG召回FAQ -->
          <rect
            x="390"
            y="380"
            width="300"
            height="80"
            rx="8"
            fill="url(#ragGradient)"
            stroke="var(--rag-border)"
            stroke-width="2"
            filter="url(#shadow)"
          />
          <text
            x="540"
            y="410"
            font-size="18"
            text-anchor="middle"
            font-weight="bold"
          >
            RAG召回FAQ
          </text>
          <text
            x="540"
            y="435"
            font-size="14"
            text-anchor="middle"
          >
            根据客户情况匹配相关FAQ
          </text>
          
          <!-- 整体大框 -->
          <rect
            x="40"
            y="500"
            width="1000"
            height="500"
            rx="15"
            fill="none"
            stroke="#495057"
            stroke-width="2"
            stroke-dasharray="10,5"
          />
          <text
            x="540"
            y="990"
            font-size="16"
            text-anchor="middle"
            font-weight="bold"
            fill="#495057"
          >
            AI催收系统2.0
          </text>

          <!-- 4. 风格判断Agent与话术选择系统并排 -->
          <rect
            x="60"
            y="520"
            width="400"
            height="460"
            rx="10"
            fill="url(#agentGradient)"
            stroke="var(--agent-border)"
            stroke-width="2"
            filter="url(#shadow)"
          />
          <text
            x="260"
            y="550"
            font-size="18"
            text-anchor="middle"
            font-weight="bold"
          >
            风格判断Agent
          </text>

          <rect
            x="620"
            y="520"
            width="400"
            height="460"
            rx="10"
            fill="url(#systemGradient)"
            stroke="var(--system-border)"
            stroke-width="2"
            filter="url(#shadow)"
          />
          <text
            x="820"
            y="550"
            font-size="18"
            text-anchor="middle"
            font-weight="bold"
          >
            话术选择Agent
          </text>

          <!-- 连接线：RAG召回到两个系统 -->
          <path
            d="M 540,460 L 540,480 L 260,480 L 260,520"
            fill="none"
            stroke="#6c757d"
            stroke-width="2"
            marker-end="url(#arrowhead)"
          />
          <path
            d="M 540,460 L 540,480 L 820,480 L 820,520"
            fill="none"
            stroke="#6c757d"
            stroke-width="2"
            marker-end="url(#arrowhead)"
          />

          <!-- 系统间双向箭头 -->
          <line
            x1="460"
            y1="700"
            x2="620"
            y2="700"
            stroke="#6c757d"
            stroke-width="2"
            marker-end="url(#arrowhead)"
          />
          <text x="540" y="695" font-size="12" text-anchor="middle" fill="#555">
            风格选择
          </text>

          <!-- 风格判断Agent内部组件 -->
          <!-- 九宫格评估 -->
          <rect
            x="90"
            y="580"
            width="340"
            height="100"
            rx="8"
            fill="#f8f9fa"
            stroke="#6c757d"
            stroke-width="2"
          />
          <text
            x="260"
            y="610"
            font-size="16"
            text-anchor="middle"
            font-weight="bold"
          >
            九宫格评估
          </text>
          <text x="260" y="640" font-size="14" text-anchor="middle">
            还款能力×还款意愿
          </text>
          <text x="260" y="660" font-size="12" text-anchor="middle" fill="#666">
            (高、中、低)×(高、中、低)
          </text>

          <!-- 意图大类识别 -->
          <rect
            x="90"
            y="690"
            width="340"
            height="80"
            rx="8"
            fill="#f5f0ff"
            stroke="#9575cd"
            stroke-width="2"
          />
          <text
            x="260"
            y="720"
            font-size="16"
            text-anchor="middle"
            font-weight="bold"
          >
            意图大类识别
          </text>
          <text x="260" y="750" font-size="14" text-anchor="middle">
            资金困难/承诺还款/拖延时间
          </text>

          <!-- 风格选择结果 -->
          <text
            x="260"
            y="800"
            font-size="16"
            text-anchor="middle"
            font-weight="bold"
          >
            风格选择结果
          </text>

          <rect
            x="90"
            y="815"
            width="100"
            height="70"
            rx="5"
            fill="#ffebee"
            stroke="#ef5350"
            stroke-width="1.5"
          />
          <text
            x="140"
            y="845"
            font-size="14"
            text-anchor="middle"
            font-weight="bold"
          >
            施压风格
          </text>
          <text x="140" y="870" font-size="12" text-anchor="middle" fill="#444">
            能力强,意愿弱
          </text>

          <rect
            x="210"
            y="815"
            width="100"
            height="70"
            rx="5"
            fill="#e8f5e9"
            stroke="#66bb6a"
            stroke-width="1.5"
          />
          <text
            x="260"
            y="845"
            font-size="14"
            text-anchor="middle"
            font-weight="bold"
          >
            共情风格
          </text>
          <text x="260" y="870" font-size="12" text-anchor="middle" fill="#444">
            能力弱,意愿强
          </text>

          <rect
            x="330"
            y="815"
            width="100"
            height="70"
            rx="5"
            fill="#e3f2fd"
            stroke="#42a5f5"
            stroke-width="1.5"
          />
          <text
            x="380"
            y="845"
            font-size="14"
            text-anchor="middle"
            font-weight="bold"
          >
            通用风格
          </text>
          <text x="380" y="870" font-size="12" text-anchor="middle" fill="#444">
            其他情况
          </text>

          <!-- 结束规则判断 -->
          <rect
            x="90"
            y="895"
            width="340"
            height="80"
            rx="8"
            fill="#f0f0f0"
            stroke="#6c757d"
            stroke-width="2"
          />
          <text
            x="260"
            y="925"
            font-size="16"
            text-anchor="middle"
            font-weight="bold"
          >
            结束规则判断
          </text>
          <text x="260" y="954" font-size="13" text-anchor="middle">
            承诺还款 OR 通话轮次>8轮
          </text>

          <!-- 风格评估结果流向指示 -->
          <line
            x1="260"
            y1="680"
            x2="260"
            y2="695"
            stroke="#6c757d"
            stroke-width="1.5"
            stroke-dasharray="4,2"
            marker-end="url(#arrowhead)"
          />
          
          <!-- 意图大类到风格选择结果 -->
          <line
            x1="260"
            y1="770"
            x2="260"
            y2="785"
            stroke="#6c757d"
            stroke-width="1.5"
            stroke-dasharray="4,2"
            marker-end="url(#arrowhead)"
          />
          
          <!-- 意图大类到话术选择系统的连线 -->
          <path
            d="M 430,730 L 550,730 L 650,640"
            fill="none"
            stroke="#6c757d"
            stroke-width="2"
            marker-end="url(#arrowhead)"
          />
          <text x="550" y="720" font-size="12" text-anchor="middle" fill="#555">
            意图传递
          </text>

          <!-- 结束规则判断到对话结果判断 -->
          <path
            d="M 260,1010 V 1085 H 440"
            fill="none"
            stroke="#6c757d"
            stroke-width="2"
            marker-end="url(#arrowhead)"
          />

          <!-- 加个标签包裹结束规则 -->
          <rect
            x="270"
            y="1030"
            width="70"
            height="25"
            rx="12"
            fill="white"
            stroke="#6c757d"
            stroke-width="1"
          />
          <text x="305" y="1046" font-size="12" text-anchor="middle" fill="#555">结束规则</text>

          <!-- 话术选择系统内部组件 -->
          <!-- 话术包分类 -->
          <rect
            x="650"
            y="580"
            width="340"
            height="80"
            rx="8"
            fill="#f8f9fa"
            stroke="#6c757d"
            stroke-width="2"
          />
          <text
            x="820"
            y="610"
            font-size="16"
            text-anchor="middle"
            font-weight="bold"
          >
            话术包分类
          </text>
          <text x="820" y="640" font-size="14" text-anchor="middle">
            根据style选择对应风格话术
          </text>

          <!-- 话术包 -->
          <rect
            x="650"
            y="680"
            width="100"
            height="60"
            rx="5"
            fill="url(#categoryGradient)"
            stroke="var(--category-border)"
            stroke-width="1.5"
          />
          <text
            x="700"
            y="710"
            font-size="14"
            text-anchor="middle"
            font-weight="bold"
          >
            资金困难
          </text>

          <rect
            x="770"
            y="680"
            width="100"
            height="60"
            rx="5"
            fill="url(#categoryGradient)"
            stroke="var(--category-border)"
            stroke-width="1.5"
          />
          <text
            x="820"
            y="710"
            font-size="14"
            text-anchor="middle"
            font-weight="bold"
          >
            承诺还款
          </text>

          <rect
            x="890"
            y="680"
            width="100"
            height="60"
            rx="5"
            fill="url(#categoryGradient)"
            stroke="var(--category-border)"
            stroke-width="1.5"
          />
          <text
            x="940"
            y="710"
            font-size="14"
            text-anchor="middle"
            font-weight="bold"
          >
            拖延时间
          </text>

          <!-- 具体话术示例（资金困难） -->
          <rect
            x="650"
            y="760"
            width="100"
            height="50"
            rx="5"
            fill="url(#scriptGradient)"
            stroke="var(--script-border)"
            stroke-width="1.5"
          />
          <text
            x="700"
            y="785"
            font-size="12"
            text-anchor="middle"
            font-style="italic"
          >
            话术1: 分期还款
          </text>

          <rect
            x="650"
            y="820"
            width="100"
            height="50"
            rx="5"
            fill="url(#faq-content-bg)"
            stroke="var(--faq-content-border)"
            stroke-width="1.5"
          />
          <text
            x="700"
            y="840"
            font-size="12"
            text-anchor="middle"
            font-weight="bold"
          >
            FAQ内容整合
          </text>
          <text
            x="700"
            y="855"
            font-size="10"
            text-anchor="middle"
            font-style="italic"
          >
            来自RAG召回
          </text>

          <!-- 具体话术示例（承诺还款） -->
          <rect
            x="770"
            y="760"
            width="100"
            height="50"
            rx="5"
            fill="url(#scriptGradient)"
            stroke="var(--script-border)"
            stroke-width="1.5"
          />
          <text
            x="820"
            y="785"
            font-size="12"
            text-anchor="middle"
            font-style="italic"
          >
            话术1: 确认时间
          </text>

          <rect
            x="770"
            y="820"
            width="100"
            height="50"
            rx="5"
            fill="url(#faq-content-bg)"
            stroke="var(--faq-content-border)"
            stroke-width="1.5"
          />
          <text
            x="820"
            y="840"
            font-size="12"
            text-anchor="middle"
            font-weight="bold"
          >
            FAQ内容整合
          </text>
          <text
            x="820"
            y="855"
            font-size="10"
            text-anchor="middle"
            font-style="italic"
          >
            来自RAG召回
          </text>

          <!-- 具体话术示例（拖延时间） -->
          <rect
            x="890"
            y="760"
            width="100"
            height="50"
            rx="5"
            fill="url(#scriptGradient)"
            stroke="var(--script-border)"
            stroke-width="1.5"
          />
          <text
            x="940"
            y="785"
            font-size="12"
            text-anchor="middle"
            font-style="italic"
          >
            话术1: 再次施压
          </text>
          
          <!-- <rect
            x="890"
            y="820"
            width="100"
            height="50"
            rx="5"
            fill="url(#faq-content-bg)"
            stroke="var(--faq-content-border)"
            stroke-width="1.5"
          />
          <text
            x="940"
            y="840"
            font-size="12"
            text-anchor="middle"
            font-weight="bold"
          >
            FAQ内容整合
          </text>
          <text
            x="940"
            y="855"
            font-size="10"
            text-anchor="middle"
            font-style="italic"
          >
            来自RAG召回
          </text> -->

          <!-- 更多话术提示 -->
          <text
            x="940"
            y="845"
            font-size="18"
            text-anchor="middle"
            font-weight="bold"
            fill="#666"
          >
            ...
          </text>
          <text x="940" y="865" font-size="12" text-anchor="middle" fill="#666">
            更多话术包
          </text>

          <!-- 流程指示线 -->
          <line
            x1="820"
            y1="660"
            x2="820"
            y2="680"
            stroke="#6c757d"
            stroke-width="1.5"
            marker-end="url(#arrowhead)"
          />

          <line
            x1="700"
            y1="740"
            x2="700"
            y2="760"
            stroke="#6c757d"
            stroke-width="1"
            marker-end="url(#arrowhead)"
          />

          <line
            x1="820"
            y1="740"
            x2="820"
            y2="760"
            stroke="#6c757d"
            stroke-width="1"
            marker-end="url(#arrowhead)"
          />

          <line
            x1="940"
            y1="740"
            x2="940"
            y2="760"
            stroke="#6c757d"
            stroke-width="1"
            marker-end="url(#arrowhead)"
          />

          <!-- 5. 对话结果判断 -->
          <rect
            x="440"
            y="1050"
            width="200"
            height="70"
            rx="8"
            fill="#f8f9fa"
            stroke="#6c757d"
            stroke-width="2"
            filter="url(#shadow)"
          />
          <text
            x="540"
            y="1080"
            font-size="18"
            text-anchor="middle"
            font-weight="bold"
          >
            对话结果判断
          </text>
          <text x="540" y="1100" font-size="14" text-anchor="middle">
            是否满足结束条件
          </text>

          <!-- 系统到对话结果判断 -->
          <path
            d="M 540,1000 L 540,1050"
            fill="none"
            stroke="#6c757d"
            stroke-width="2"
            marker-end="url(#arrowhead)"
          />

          <!-- 6. 结束通话 -->
          <rect
            x="440"
            y="1170"
            width="200"
            height="60"
            rx="30"
            fill="#ff6b6b"
            filter="url(#shadow)"
          />
          <text
            x="540"
            y="1205"
            font-size="18"
            text-anchor="middle"
            fill="white"
            font-weight="bold"
          >
            结束通话
          </text>

          <!-- 对话结果判断到结束 -->
          <line
            x1="540"
            y1="1120"
            x2="540"
            y2="1170"
            stroke="#6c757d"
            stroke-width="2"
            marker-end="url(#arrowhead)"
          />

          <!-- 对话循环路径 -->
          <path
            d="M 640,1075 C 700,1075 700,990 600,990"
            fill="none"
            stroke="#6c757d"
            stroke-width="2"
            stroke-dasharray="5,3"
            marker-end="url(#arrowhead)"
          />

          <!-- 循环标签 -->
          <rect
            x="670"
            y="1030"
            width="70"
            height="25"
            rx="12"
            fill="white"
            stroke="#6c757d"
            stroke-width="1"
          />
          <text
            x="705"
            y="1048"
            font-size="12"
            text-anchor="middle"
            fill="#6c757d"
          >
            继续对话
          </text>

          <!-- 结束标签 -->
          <rect
            x="545"
            y="1135"
            width="70"
            height="25"
            rx="12"
            fill="white"
            stroke="#6c757d"
            stroke-width="1"
          />
          <text
            x="580"
            y="1153"
            font-size="12"
            text-anchor="middle"
            fill="#6c757d"
          >
            满足条件
          </text>
          
          <!-- 7. 事后分析Agent -->
          <rect
            x="340"
            y="1280"
            width="400"
            height="120"
            rx="10"
            fill="url(#analysisGradient)"
            stroke="var(--analysis-border)"
            stroke-width="2"
            filter="url(#shadow)"
          />
          <text
            x="540"
            y="1310"
            font-size="18"
            text-anchor="middle"
            font-weight="bold"
          >
            事后分析Agent监工
          </text>
          <text x="540" y="1340" font-size="14" text-anchor="middle">
            全面审视拨打情况
          </text>
          <text x="540" y="1365" font-size="13" text-anchor="middle">
            分析对话效果/统计承诺还款情况/提炼改进建议
          </text>
          
          <!-- 结束通话到事后分析 -->
          <line
            x1="540"
            y1="1230"
            x2="540"
            y2="1280"
            stroke="#6c757d"
            stroke-width="2"
            marker-end="url(#arrowhead)"
          />
        </svg>
      </div>

      <div class="legend">
        <div class="legend-item">
          <div
            class="legend-color"
            style="
              background-color: var(--agent-bg);
              border: 1px solid var(--agent-border);
            "
          ></div>
          <span>风格判断Agent</span>
        </div>
        <div class="legend-item">
          <div
            class="legend-color"
            style="
              background-color: var(--system-bg);
              border: 1px solid var(--system-border);
            "
          ></div>
          <span>话术选择系统</span>
        </div>
        <div class="legend-item">
          <div
            class="legend-color"
            style="
              background-color: var(--category-bg);
              border: 1px solid var(--category-border);
            "
          ></div>
          <span>话术包类别</span>
        </div>
        <div class="legend-item">
          <div
            class="legend-color"
            style="
              background-color: var(--script-bg);
              border: 1px solid var(--script-border);
            "
          ></div>
          <span>具体话术</span>
        </div>
        <div class="legend-item">
          <div
            class="legend-color"
            style="
              background-color: var(--rag-bg);
              border: 1px solid var(--rag-border);
            "
          ></div>
          <span>RAG召回FAQ</span>
        </div>
        <div class="legend-item">
          <div
            class="legend-color"
            style="
              background-color: var(--tag-bg);
              border: 1px solid var(--tag-border);
            "
          ></div>
          <span>客户标签</span>
        </div>
        <div class="legend-item">
          <div
            class="legend-color"
            style="
              background-color: var(--analysis-bg);
              border: 1px solid var(--analysis-border);
            "
          ></div>
          <span>事后分析Agent</span>
        </div>
        <div class="legend-item">
          <div
            class="legend-color"
            style="
              background-color: var(--faq-content-bg);
              border: 1px solid var(--faq-content-border);
            "
          ></div>
          <span>FAQ内容整合</span>
        </div>
        <div class="legend-item">
          <div
            class="legend-color"
            style="
              background-color: var(--external-tag-bg);
              border: 1px solid var(--external-tag-border);
            "
          ></div>
          <span>外部传入标签</span>
        </div>
      </div>
    </div>
  </body>
</html>
